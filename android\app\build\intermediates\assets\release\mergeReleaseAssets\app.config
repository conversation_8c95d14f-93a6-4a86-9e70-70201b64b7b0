{"name": "Foody<PERSON>ueen Captain", "slug": "captain-app", "version": "2.0.1", "orientation": "portrait", "icon": "./assets/images/captain.png", "scheme": "<PERSON><PERSON><PERSON>", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/captain.png", "backgroundColor": "#000000"}, "edgeToEdgeEnabled": true, "permissions": ["INTERNET"], "usesCleartextTraffic": true, "package": "com.kingsanu.captainapp"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favcaptain.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/captain.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "0f909650-8c78-4f88-b3d6-2d0c123d1e8e"}}, "sdkVersion": "53.0.0", "platforms": ["ios", "android", "web"], "androidStatusBar": {"backgroundColor": "#ffffff"}}