@echo off
"C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HK:\\2025\\captainapp_native\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=x86" ^
  "-DCMAKE_ANDROID_ARCH_ABI=x86" ^
  "-DANDROID_NDK=C:\\Android\\android-sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_ANDROID_NDK=C:\\Android\\android-sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Android\\android-sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\x86" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\x86" ^
  "-DCMAKE_BUILD_TYPE=RelWithDebInfo" ^
  "-DCMAKE_FIND_ROOT_PATH=K:\\2025\\captainapp_native\\android\\app\\.cxx\\RelWithDebInfo\\5y6g5o2j\\prefab\\x86\\prefab" ^
  "-BK:\\2025\\captainapp_native\\android\\app\\.cxx\\RelWithDebInfo\\5y6g5o2j\\x86" ^
  -GNinja ^
  "-DPROJECT_BUILD_DIR=K:\\2025\\captainapp_native\\android\\app\\build" ^
  "-DPROJECT_ROOT_DIR=K:\\2025\\captainapp_native\\android" ^
  "-DREACT_ANDROID_DIR=K:\\2025\\captainapp_native\\node_modules\\react-native\\ReactAndroid" ^
  "-DANDROID_STL=c++_shared" ^
  "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
