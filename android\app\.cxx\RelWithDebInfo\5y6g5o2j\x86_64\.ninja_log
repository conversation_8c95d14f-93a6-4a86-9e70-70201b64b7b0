# ninja log v5
5	60	0	K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64/CMakeFiles/cmake.verify_globs	7349e535514c7867
188	10621	7724999535907963	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	e6439347333b437c
49	10770	7724999537613108	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	f14f6e6cf05708de
108	10919	7724999539093579	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	93c3bac852c3a004
89	11773	7724999547224544	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	3026b1bcfdea15a4
28	12749	7724999557404893	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	182df85985b8a181
57	12904	7724999558520082	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	9c620d15cc1195c3
168	13432	7724999564049317	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	f4aeefd5681b39be
65	14216	7724999571960715	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	e99077daa4c96ba8
146	14821	7724999577859484	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	d6e59158ed479f0d
14	15283	7724999582070321	CMakeFiles/appmodules.dir/OnLoad.cpp.o	bb281077badce2eb
36	16107	7724999590674407	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	64640193db245dbc
137	16383	7724999593399647	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	6b4b928c37208c0c
177	17191	7724999601429598	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	5c8d9e894a89984
20	17286	7724999602459688	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	ddee9a8bca8f836a
118	17639	7724999605940054	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o	3d727d1bd06e2ab6
10653	17806	7724999607808193	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	e40ca5841b68d591
73	18358	7724999613242855	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	b5a5f79dd5a4608b
155	18648	7724999615939741	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	b8cecf50ad5d7348
128	21277	7724999642363347	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	be94494754c881e
10921	21468	7724999644535847	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	9bf6891fe701eab9
12753	22723	7724999656681466	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/77bb10b1f7b231f8ea34c58ca2cff267/react/renderer/components/safeareacontext/EventEmitters.cpp.o	44cbb6891dea9da4
10772	23835	7724999667808762	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	ed4ae4c1a83c913b
16108	23947	7724999669269044	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6b94d0f9ff68ad1175300e2f7d616c00/jni/react/renderer/components/safeareacontext/States.cpp.o	be80b319cf7a4316
14822	28791	7724999715726495	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6b94d0f9ff68ad1175300e2f7d616c00/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	4b5739847ac34683
11775	29329	7724999722798297	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ef35edef4d591f964fbaa59cce3e2c99/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	159f2b89792b9420
99	30515	7724999734266337	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	43eecf56e638a211
17807	30549	7724999734692567	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f7c24081ed5d60b67b13d5145b437c5d/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	5ec9feea542cbfbf
17289	30758	7724999736828557	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a3ca1ecc8f5cceea5b5f876add4a7ea8/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	c0beb91174531ef6
13439	31550	7724999745178091	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/06d7ba90cd5d80763fb15e7dfef99f10/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	4f3a508829a1458b
21470	32473	7724999754275501	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/987a4ac9eca7185125f7a2e035c5c380/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	20dc90013bc6f3a
15284	33362	7724999762854571	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8987378e8d071280da2bd42439cce0f1/components/safeareacontext/safeareacontextJSI-generated.cpp.o	12c17f9fa04eb403
21283	33875	7724999768002563	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/987a4ac9eca7185125f7a2e035c5c380/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	ab96bd1709477c0a
22729	33909	7724999768062539	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c6fa9b3880b9feebd672d8a9fed01b09/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	873e706f9bad6631
16385	36089	7724999790346193	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e19bf90ac2568e1f98aa326c0ca69e65/generated/source/codegen/jni/safeareacontext-generated.cpp.o	9e60aef814b27db5
18367	36293	7724999792514374	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f7c24081ed5d60b67b13d5145b437c5d/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	e52c5d63517073ec
23950	37608	7724999805650907	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0b53daeaf6727b2a333a9942f7e191b1/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	1c6d7275c03bf76d
14217	37663	7724999805520888	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6b94d0f9ff68ad1175300e2f7d616c00/jni/react/renderer/components/safeareacontext/Props.cpp.o	86b2993351813fab
12910	38926	7724999818509970	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8d690ee4b9629d95eab0a361352c9701/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	1462d4ea21698bc2
28810	38986	7724999819219062	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f7c24081ed5d60b67b13d5145b437c5d/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	4bab63f8881149d5
17195	39201	7724999821559430	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7d7068772185a04e70f56021641672af/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	de84d288b5cac9d0
17649	39369	7724999822923682	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0b53daeaf6727b2a333a9942f7e191b1/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	359105116a1ee25f
38937	39406	7724999823692335	K:/2025/captainapp_native/android/app/build/intermediates/cxx/RelWithDebInfo/5y6g5o2j/obj/x86_64/libreact_codegen_safeareacontext.so	9b61d380b74dcf6c
30759	40391	7724999833651330	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	e6fa284438ddaeef
23838	41604	7724999845431543	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a3ca1ecc8f5cceea5b5f876add4a7ea8/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	36c342bad1a05f09
30516	46101	7724999890614232	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	d68a1f2ca4599f30
32475	46213	7724999891687701	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	2f9e9eadb40d7bce
38987	50243	7724999932502692	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o	9b22a241aa4ad50b
39370	50449	7724999934382747	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	270c293830a094eb
31551	50476	7724999934597897	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	b6e4007e8c53849e
33363	52448	7724999953912010	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	9ae7f0e08cb5862e
33913	52958	7724999959467183	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o	ec0de0c90465e737
37664	53647	7724999966492025	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o	732aa3c85cebefe1
36301	54145	7724999971452692	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o	5de69dc66bdaa73d
18650	54215	7724999971924853	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/987a4ac9eca7185125f7a2e035c5c380/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	9f191d9f6ce0409e
33881	54534	7724999975260521	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o	7aca337eab9984b8
39407	55030	7724999980349128	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	a93e31818dbfe6c4
39207	55068	7724999980769124	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	f7c041232de0e8af
36092	56620	7724999996129109	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o	f209bd0170b56191
37624	57140	7725000001369132	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o	ca776a5e08aa0bb8
30550	57164	7725000001559195	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/987a4ac9eca7185125f7a2e035c5c380/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	72a796d69d843470
29331	62077	7725000050522500	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1e074f9162f909ec3bfcb89dffcb2108/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	61347bcb7b70e473
62077	62282	7725000052872565	K:/2025/captainapp_native/android/app/build/intermediates/cxx/RelWithDebInfo/5y6g5o2j/obj/x86_64/libreact_codegen_rnscreens.so	b2f6ae5bed92545e
5	67061	7725000099786393	CMakeFiles/appmodules.dir/K_/2025/captainapp_native/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	879fa63ddb4ed9f1
67062	67253	7725000102466686	K:/2025/captainapp_native/android/app/build/intermediates/cxx/RelWithDebInfo/5y6g5o2j/obj/x86_64/libappmodules.so	116c4693a0b99955
1	29	0	K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64/CMakeFiles/cmake.verify_globs	7349e535514c7867
2	28	0	K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64/CMakeFiles/cmake.verify_globs	7349e535514c7867
1	28	0	K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64/CMakeFiles/cmake.verify_globs	7349e535514c7867
2	26	0	K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64/CMakeFiles/cmake.verify_globs	7349e535514c7867
1	25	0	K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64/CMakeFiles/cmake.verify_globs	7349e535514c7867
2	27	0	K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64/CMakeFiles/cmake.verify_globs	7349e535514c7867
2	44	0	K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64/CMakeFiles/cmake.verify_globs	7349e535514c7867
