{"buildFiles": ["K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\captainapp_native\\android\\app\\.cxx\\RelWithDebInfo\\5y6g5o2j\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\captainapp_native\\android\\app\\.cxx\\RelWithDebInfo\\5y6g5o2j\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "x86", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "x86", "output": "K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\x86\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f7366e53586a12172ab374397325a4bf\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "x86", "runtimeFiles": []}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"artifactName": "react_codegen_RNCWebViewSpec", "abi": "x86", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "x86", "output": "K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\x86\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f7366e53586a12172ab374397325a4bf\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "x86", "output": "K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\x86\\libappmodules.so", "runtimeFiles": ["K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\x86\\libreact_codegen_safeareacontext.so", "K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\x86\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f7366e53586a12172ab374397325a4bf\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}, "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e": {"artifactName": "react_codegen_RNEdgeToEdge", "abi": "x86", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "x86", "runtimeFiles": []}}}