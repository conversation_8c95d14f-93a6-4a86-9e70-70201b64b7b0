{"enabled": true, "prefabPath": "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar", "packages": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab", "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\release\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\89ae9be5931f26e4698dba75fe74eeda\\transformed\\hermes-android-0.79.4-release\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f7366e53586a12172ab374397325a4bf\\transformed\\fbjni-0.7.0\\prefab"]}