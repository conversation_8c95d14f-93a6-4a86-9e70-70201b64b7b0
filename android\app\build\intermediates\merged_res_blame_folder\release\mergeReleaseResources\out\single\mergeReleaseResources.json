[{"merged": "com.kingsanu.captainapp-release-58:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_exporouter_assets_arrow_down.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_exporouter_assets_arrow_down.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_exporouter_assets_sitemap.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_exporouter_assets_sitemap.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xxxhdpi_splashscreen_logo.png.flat", "source": "com.kingsanu.captainapp-main-59:/drawable-xxxhdpi/splashscreen_logo.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_exporouter_assets_file.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_exporouter_assets_file.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable_ic_launcher_background.xml.flat", "source": "com.kingsanu.captainapp-main-59:/drawable/ic_launcher_background.xml"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_exporouter_assets_forward.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_exporouter_assets_forward.png"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.kingsanu.captainapp-release-58:/xml_network_security_config.xml.flat", "source": "com.kingsanu.captainapp-main-59:/xml/network_security_config.xml"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_splashscreen_logo.png.flat", "source": "com.kingsanu.captainapp-main-59:/drawable-mdpi/splashscreen_logo.png"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-hdpi_splashscreen_logo.png.flat", "source": "com.kingsanu.captainapp-main-59:/drawable-hdpi/splashscreen_logo.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_exporouter_assets_unmatched.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_exporouter_assets_unmatched.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xhdpi_splashscreen_logo.png.flat", "source": "com.kingsanu.captainapp-main-59:/drawable-xhdpi/splashscreen_logo.png"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_exporouter_assets_error.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_exporouter_assets_error.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_backiconmask.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_backiconmask.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_assets_images_captain.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/assets_images_captain.png"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-mdpi_node_modules_exporouter_assets_pkg.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-mdpi/node_modules_exporouter_assets_pkg.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable_rn_edit_text_material.xml.flat", "source": "com.kingsanu.captainapp-main-59:/drawable/rn_edit_text_material.xml"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xxhdpi_splashscreen_logo.png.flat", "source": "com.kingsanu.captainapp-main-59:/drawable-xxhdpi/splashscreen_logo.png"}, {"merged": "com.kingsanu.captainapp-release-58:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.kingsanu.captainapp-main-59:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.kingsanu.captainapp-release-58:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.kingsanu.captainapp-res-53:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.kingsanu.captainapp-release-58:/raw_keep.xml.flat", "source": "com.kingsanu.captainapp-res-53:/raw/keep.xml"}]