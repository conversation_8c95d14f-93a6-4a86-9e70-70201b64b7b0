{"logs": [{"outputFile": "com.kingsanu.captainapp-mergeReleaseResources-66:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6c6e309b72c7c7f21fbfce9b95a8faf6\\transformed\\core-1.13.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "39,40,41,42,43,44,45,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3525,3623,3725,3827,3931,4034,4132,14077", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "3618,3720,3822,3926,4029,4127,4241,14173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7becb12098085a58be85c10a694bf84d\\transformed\\material-1.12.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,415,493,591,680,780,899,982,1038,1103,1197,1267,1326,1416,1480,1549,1607,1676,1736,1800,1912,1971,2030,2085,2160,2283,2363,2446,2540,2627,2711,2844,2926,3007,3138,3225,3307,3365,3421,3487,3562,3642,3713,3792,3859,3934,4011,4075,4182,4276,4346,4435,4528,4602,4677,4767,4823,4902,4969,5053,5137,5199,5263,5326,5392,5492,5599,5693,5801,5863,5923,6003,6088,6169", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,77,77,97,88,99,118,82,55,64,93,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79,84,80,73", "endOffsets": "254,332,410,488,586,675,775,894,977,1033,1098,1192,1262,1321,1411,1475,1544,1602,1671,1731,1795,1907,1966,2025,2080,2155,2278,2358,2441,2535,2622,2706,2839,2921,3002,3133,3220,3302,3360,3416,3482,3557,3637,3708,3787,3854,3929,4006,4070,4177,4271,4341,4430,4523,4597,4672,4762,4818,4897,4964,5048,5132,5194,5258,5321,5387,5487,5594,5688,5796,5858,5918,5998,6083,6164,6238"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,69,70,71,76,79,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3104,3182,3260,3338,3436,4246,4346,4465,6965,7021,7086,7568,7787,7912,8002,8066,8135,8193,8262,8322,8386,8498,8557,8616,8671,8746,9089,9169,9252,9346,9433,9517,9650,9732,9813,9944,10031,10113,10171,10227,10293,10368,10448,10519,10598,10665,10740,10817,10881,10988,11082,11152,11241,11334,11408,11483,11573,11629,11708,11775,11859,11943,12005,12069,12132,12198,12298,12405,12499,12607,12669,12729,13202,13287,13368", "endLines": "5,34,35,36,37,38,46,47,48,69,70,71,76,79,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148,149,150", "endColumns": "12,77,77,77,97,88,99,118,82,55,64,93,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79,84,80,73", "endOffsets": "304,3177,3255,3333,3431,3520,4341,4460,4543,7016,7081,7175,7633,7841,7997,8061,8130,8188,8257,8317,8381,8493,8552,8611,8666,8741,8864,9164,9247,9341,9428,9512,9645,9727,9808,9939,10026,10108,10166,10222,10288,10363,10443,10514,10593,10660,10735,10812,10876,10983,11077,11147,11236,11329,11403,11478,11568,11624,11703,11770,11854,11938,12000,12064,12127,12193,12293,12400,12494,12602,12664,12724,12804,13282,13363,13437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9a9d7ace362c65c6063a55648731b1\\transformed\\appcompat-1.7.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,423,529,638,724,828,948,1025,1100,1192,1286,1381,1475,1576,1670,1766,1860,1952,2044,2129,2237,2343,2445,2556,2657,2773,2938,13116", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "418,524,633,719,823,943,1020,1095,1187,1281,1376,1470,1571,1665,1761,1855,1947,2039,2124,2232,2338,2440,2551,2652,2768,2933,3031,13197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf4e5700f050532bbd59ead7b0c07184\\transformed\\play-services-base-18.5.0\\res\\values-ur\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,457,579,686,822,945,1053,1153,1301,1407,1575,1699,1841,2006,2065,2128", "endColumns": "103,159,121,106,135,122,107,99,147,105,167,123,141,164,58,62,83", "endOffsets": "296,456,578,685,821,944,1052,1152,1300,1406,1574,1698,1840,2005,2064,2127,2211"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4625,4733,4897,5023,5134,5274,5401,5513,5769,5921,6031,6203,6331,6477,6646,6709,6776", "endColumns": "107,163,125,110,139,126,111,103,151,109,171,127,145,168,62,66,87", "endOffsets": "4728,4892,5018,5129,5269,5396,5508,5612,5916,6026,6198,6326,6472,6641,6704,6771,6859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cd10ac37c6d4674d33088c916aa639d9\\transformed\\browser-1.6.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,106", "endOffsets": "151,252,363,470"}, "to": {"startLines": "68,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6864,7180,7281,7392", "endColumns": "100,100,110,106", "endOffsets": "6960,7276,7387,7494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c0eaab12c39868587e4f18db7dfb16a1\\transformed\\react-android-0.79.5-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,269,338,418,484,552,627,704,787,866,934,1011,1093,1167,1250,1336,1412,1485,1557,1646,1717,1793,1862", "endColumns": "67,76,68,68,79,65,67,74,76,82,78,67,76,81,73,82,85,75,72,71,88,70,75,68,72", "endOffsets": "118,195,264,333,413,479,547,622,699,782,861,929,1006,1088,1162,1245,1331,1407,1480,1552,1641,1712,1788,1857,1930"}, "to": {"startLines": "33,49,75,77,78,80,94,95,96,143,144,145,146,151,152,153,154,155,156,157,158,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3036,4548,7499,7638,7707,7846,8869,8937,9012,12809,12892,12971,13039,13442,13524,13598,13681,13767,13843,13916,13988,14178,14249,14325,14394", "endColumns": "67,76,68,68,79,65,67,74,76,82,78,67,76,81,73,82,85,75,72,71,88,70,75,68,72", "endOffsets": "3099,4620,7563,7702,7782,7907,8932,9007,9084,12887,12966,13034,13111,13519,13593,13676,13762,13838,13911,13983,14072,14244,14320,14389,14462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8c47125c9a335e989accf2286b6eb952\\transformed\\play-services-basement-18.4.0\\res\\values-ur\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5617", "endColumns": "151", "endOffsets": "5764"}}]}]}