<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-linking" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\expo-linking\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\expo-file-system\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\expo-constants\android\build\intermediates\library_assets\release\packageReleaseAssets\out"><file name="app.config" path="K:\2025\captainapp_native\node_modules\expo-constants\android\build\intermediates\library_assets\release\packageReleaseAssets\out\app.config"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\react-native-reanimated\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\expo\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-edge-to-edge" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\react-native-edge-to-edge\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-webview" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\react-native-screens\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\android\app\src\main\assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\android\app\src\release\assets"/></dataSet><dataSet config="assets-createBundleReleaseJsAndAssets" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\android\app\build\generated\assets\createBundleReleaseJsAndAssets"><file name="index.android.bundle" path="K:\2025\captainapp_native\android\app\build\generated\assets\createBundleReleaseJsAndAssets\index.android.bundle"/></source></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\android\app\build\intermediates\shader_assets\release\compileReleaseShaders\out"/></dataSet></merger>