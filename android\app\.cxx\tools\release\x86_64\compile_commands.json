[{"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IK:/2025/captainapp_native/android/app/build/generated/autolinking/src/main/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\K_\\2025\\captainapp_native\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c K:\\2025\\captainapp_native\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "K:\\2025\\captainapp_native\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IK:/2025/captainapp_native/android/app/build/generated/autolinking/src/main/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\rngesturehandler_codegenJSI-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8d690ee4b9629d95eab0a361352c9701\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\ef35edef4d591f964fbaa59cce3e2c99\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\06d7ba90cd5d80763fb15e7dfef99f10\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\77bb10b1f7b231f8ea34c58ca2cff267\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\6b94d0f9ff68ad1175300e2f7d616c00\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\6b94d0f9ff68ad1175300e2f7d616c00\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\6b94d0f9ff68ad1175300e2f7d616c00\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8987378e8d071280da2bd42439cce0f1\\components\\safeareacontext\\safeareacontextJSI-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\e19bf90ac2568e1f98aa326c0ca69e65\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\f7c24081ed5d60b67b13d5145b437c5d\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7d7068772185a04e70f56021641672af\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a3ca1ecc8f5cceea5b5f876add4a7ea8\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\0b53daeaf6727b2a333a9942f7e191b1\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\f7c24081ed5d60b67b13d5145b437c5d\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\0b53daeaf6727b2a333a9942f7e191b1\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\f7c24081ed5d60b67b13d5145b437c5d\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a3ca1ecc8f5cceea5b5f876add4a7ea8\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\1e074f9162f909ec3bfcb89dffcb2108\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\987a4ac9eca7185125f7a2e035c5c380\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\987a4ac9eca7185125f7a2e035c5c380\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\987a4ac9eca7185125f7a2e035c5c380\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\987a4ac9eca7185125f7a2e035c5c380\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\c6fa9b3880b9feebd672d8a9fed01b09\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\RNCWebViewSpec-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\RNEdgeToEdge-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\RNEdgeToEdge-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\RNEdgeToEdge-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp"}, {"directory": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86_64", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -IK:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/55e5e2b47fc8f51b895ce8757651a2de/transformed/react-android-0.79.4-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp.o -c K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp", "file": "K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp"}]