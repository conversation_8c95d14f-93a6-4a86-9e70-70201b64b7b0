{"logs": [{"outputFile": "com.kingsanu.captainapp-mergeReleaseResources-56:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\25ad4bdeddd79d498a0e9dfe4af5a7d2\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "39,40,41,42,43,44,45,141", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3618,3714,3817,3916,4014,4121,4236,12254", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3709,3812,3911,4009,4116,4231,4359,12350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\44d5193287b18dec49d9cd0bdd73b6e9\\transformed\\browser-1.6.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,274,381", "endColumns": "116,101,106,103", "endOffsets": "167,269,376,480"}, "to": {"startLines": "50,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4755,5103,5205,5312", "endColumns": "116,101,106,103", "endOffsets": "4867,5200,5307,5411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1cc5e9e2cf9bf5411b10463ffe5bdf1d\\transformed\\appcompat-1.7.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,435,537,652,741,852,973,1052,1128,1226,1326,1421,1515,1622,1722,1824,1918,2016,2114,2195,2303,2406,2505,2621,2724,2829,2986,11259", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "430,532,647,736,847,968,1047,1123,1221,1321,1416,1510,1617,1717,1819,1913,2011,2109,2190,2298,2401,2500,2616,2719,2824,2981,3083,11336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,210,282,350,434,504,571,647,725,807,887,958,1040,1122,1200,1289,1379,1460,1532,1602,1696,1771,1854,1923", "endColumns": "74,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "125,205,277,345,429,499,566,642,720,802,882,953,1035,1117,1195,1284,1374,1455,1527,1597,1691,1766,1849,1918,1996"}, "to": {"startLines": "33,49,57,59,60,62,76,77,78,125,126,127,128,133,134,135,136,137,138,139,140,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3088,4675,5416,5556,5624,5769,6881,6948,7024,10944,11026,11106,11177,11598,11680,11758,11847,11937,12018,12090,12160,12355,12430,12513,12582", "endColumns": "74,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "3158,4750,5483,5619,5703,5834,6943,7019,7097,11021,11101,11172,11254,11675,11753,11842,11932,12013,12085,12155,12249,12425,12508,12577,12655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1688e4f4998421f6cdbc59f17cef2f61\\transformed\\material-1.12.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,360,446,530,633,727,836,954,1038,1097,1161,1269,1337,1398,1506,1573,1659,1717,1801,1868,1922,2045,2107,2170,2224,2312,2440,2526,2618,2721,2813,2895,3027,3107,3188,3344,3433,3517,3574,3626,3692,3777,3865,3936,4016,4085,4162,4242,4310,4425,4524,4607,4699,4793,4867,4953,5047,5097,5180,5246,5331,5418,5481,5546,5609,5678,5786,5884,5982,6079,6140,6196,6282,6374,6457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,85,83,102,93,108,117,83,58,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,155,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85,91,82,81", "endOffsets": "267,355,441,525,628,722,831,949,1033,1092,1156,1264,1332,1393,1501,1568,1654,1712,1796,1863,1917,2040,2102,2165,2219,2307,2435,2521,2613,2716,2808,2890,3022,3102,3183,3339,3428,3512,3569,3621,3687,3772,3860,3931,4011,4080,4157,4237,4305,4420,4519,4602,4694,4788,4862,4948,5042,5092,5175,5241,5326,5413,5476,5541,5604,5673,5781,5879,5977,6074,6135,6191,6277,6369,6452,6534"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,51,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3163,3251,3337,3421,3524,4364,4473,4591,4872,4931,4995,5488,5708,5839,5947,6014,6100,6158,6242,6309,6363,6486,6548,6611,6665,6753,7102,7188,7280,7383,7475,7557,7689,7769,7850,8006,8095,8179,8236,8288,8354,8439,8527,8598,8678,8747,8824,8904,8972,9087,9186,9269,9361,9455,9529,9615,9709,9759,9842,9908,9993,10080,10143,10208,10271,10340,10448,10546,10644,10741,10802,10858,11341,11433,11516", "endLines": "5,34,35,36,37,38,46,47,48,51,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,130,131,132", "endColumns": "12,87,85,83,102,93,108,117,83,58,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,155,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85,91,82,81", "endOffsets": "317,3246,3332,3416,3519,3613,4468,4586,4670,4926,4990,5098,5551,5764,5942,6009,6095,6153,6237,6304,6358,6481,6543,6606,6660,6748,6876,7183,7275,7378,7470,7552,7684,7764,7845,8001,8090,8174,8231,8283,8349,8434,8522,8593,8673,8742,8819,8899,8967,9082,9181,9264,9356,9450,9524,9610,9704,9754,9837,9903,9988,10075,10138,10203,10266,10335,10443,10541,10639,10736,10797,10853,10939,11428,11511,11593"}}]}]}