{"installationFolder": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\release\\prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "3.17.5", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "x86_64", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\RelWithDebInfo\\434u4d6h\\obj\\x86_64\\libreanimated.so", "abiAndroidGradleBuildJsonFile": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\434u4d6h\\x86_64\\android_gradle_build.json"}]}, {"moduleName": "worklets", "moduleHeaders": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\worklets", "moduleExportLibraries": [], "abis": [{"abiName": "x86_64", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\RelWithDebInfo\\434u4d6h\\obj\\x86_64\\libworklets.so", "abiAndroidGradleBuildJsonFile": "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\.cxx\\RelWithDebInfo\\434u4d6h\\x86_64\\android_gradle_build.json"}]}]}}