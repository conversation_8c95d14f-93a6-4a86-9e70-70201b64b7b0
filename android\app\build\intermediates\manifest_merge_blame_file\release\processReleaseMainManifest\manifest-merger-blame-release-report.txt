1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.kingsanu.captainapp"
4    android:versionCode="1"
5    android:versionName="2.1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:2:3-62
11-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:2:20-60
12    <uses-permission android:name="android.permission.INTERNET" />
12-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:3:3-64
12-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:3:20-62
13    <uses-permission
13-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:4:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:4:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69d0959ce4cc42db4ae43c9749354080\transformed\expo.modules.image-2.3.0\AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:5:3-68
16-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:5:20-66
17    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
17-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:6:3-75
17-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:6:20-73
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:7:3-63
18-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:7:20-61
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:8:3-78
19-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:8:20-76
20
21    <queries>
21-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:9:3-15:13
22        <intent>
22-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:10:5-14:14
23            <action android:name="android.intent.action.VIEW" />
23-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:11:7-58
23-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:11:15-56
24
25            <category android:name="android.intent.category.BROWSABLE" />
25-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:12:7-67
25-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:12:17-65
26
27            <data android:scheme="https" />
27-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:13:7-37
27-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:13:13-35
28        </intent>
29        <!-- Query open documents -->
30        <intent>
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
31            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
31-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
31-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
32        </intent>
33        <intent>
33-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\58435b24f0bb44a49039f003dc31ebb2\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:8:9-12:18
34
35            <!-- Required for opening tabs if targeting API 30 -->
36            <action android:name="android.support.customtabs.action.CustomTabsService" />
36-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\58435b24f0bb44a49039f003dc31ebb2\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:13-90
36-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\58435b24f0bb44a49039f003dc31ebb2\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:21-87
37        </intent>
38        <intent>
38-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4093479fe42157f0f55fead2fd8228c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
39            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
39-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4093479fe42157f0f55fead2fd8228c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
39-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4093479fe42157f0f55fead2fd8228c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
40        </intent>
41    </queries>
42    <!--
43  Allows Glide to monitor connectivity status and restart failed requests if users go from a
44  a disconnected to a connected network state.
45    -->
46    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
46-->[BareExpo:expo.modules.image:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69d0959ce4cc42db4ae43c9749354080\transformed\expo.modules.image-2.3.0\AndroidManifest.xml:12:5-79
46-->[BareExpo:expo.modules.image:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69d0959ce4cc42db4ae43c9749354080\transformed\expo.modules.image-2.3.0\AndroidManifest.xml:12:22-76
47
48    <permission
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
49        android:name="com.kingsanu.captainapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.kingsanu.captainapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
53
54    <application
54-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:3-32:17
55        android:name="com.kingsanu.captainapp.MainApplication"
55-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:16-47
56        android:allowBackup="true"
56-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:223-249
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
58        android:extractNativeLibs="false"
59        android:icon="@mipmap/ic_launcher"
59-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:81-115
60        android:label="@string/app_name"
60-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:48-80
61        android:networkSecurityConfig="@xml/network_security_config"
61-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:116-176
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:177-222
63        android:supportsRtl="true"
63-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:282-308
64        android:theme="@style/AppTheme" >
64-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:250-281
65        <meta-data
65-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:17:5-83
66            android:name="expo.modules.updates.ENABLED"
66-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:17:16-59
67            android:value="false" />
67-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:17:60-81
68        <meta-data
68-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:18:5-105
69            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
69-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:18:16-80
70            android:value="ALWAYS" />
70-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:18:81-103
71        <meta-data
71-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:19:5-99
72            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
72-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:19:16-79
73            android:value="0" />
73-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:19:80-97
74
75        <activity
75-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:20:5-31:16
76            android:name="com.kingsanu.captainapp.MainActivity"
76-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:20:15-43
77            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
77-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:20:44-134
78            android:exported="true"
78-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:20:256-279
79            android:launchMode="singleTask"
79-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:20:135-166
80            android:screenOrientation="portrait"
80-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:20:280-316
81            android:theme="@style/Theme.App.SplashScreen"
81-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:20:210-255
82            android:windowSoftInputMode="adjustResize" >
82-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:20:167-209
83            <intent-filter>
83-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:21:7-24:23
84                <action android:name="android.intent.action.MAIN" />
84-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:22:9-60
84-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:22:17-58
85
86                <category android:name="android.intent.category.LAUNCHER" />
86-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:23:9-68
86-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:23:19-66
87            </intent-filter>
88            <intent-filter>
88-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:25:7-30:23
89                <action android:name="android.intent.action.VIEW" />
89-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:11:7-58
89-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:11:15-56
90
91                <category android:name="android.intent.category.DEFAULT" />
91-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:27:9-67
91-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:27:19-65
92                <category android:name="android.intent.category.BROWSABLE" />
92-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:12:7-67
92-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:12:17-65
93
94                <data android:scheme="captainapp" />
94-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:13:7-37
94-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:13:13-35
95            </intent-filter>
96        </activity>
97
98        <provider
98-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
99            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
99-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-83
100            android:authorities="com.kingsanu.captainapp.fileprovider"
100-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
101            android:exported="false"
101-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
102            android:grantUriPermissions="true" >
102-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
103            <meta-data
103-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
104                android:name="android.support.FILE_PROVIDER_PATHS"
104-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
105                android:resource="@xml/file_provider_paths" />
105-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
106        </provider>
107
108        <meta-data
108-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
109            android:name="org.unimodules.core.AppLoader#react-native-headless"
109-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
110            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
110-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
111        <meta-data
111-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
112            android:name="com.facebook.soloader.enabled"
112-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
113            android:value="true" />
113-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
114        <meta-data
114-->[host.exp.exponent:expo.modules.camera:16.1.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\eddcfd92e1a5608e63c6ff0eb65c6ff4\transformed\expo.modules.camera-16.1.10\AndroidManifest.xml:11:9-13:42
115            android:name="com.google.mlkit.vision.DEPENDENCIES"
115-->[host.exp.exponent:expo.modules.camera:16.1.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\eddcfd92e1a5608e63c6ff0eb65c6ff4\transformed\expo.modules.camera-16.1.10\AndroidManifest.xml:12:13-64
116            android:value="barcode_ui" />
116-->[host.exp.exponent:expo.modules.camera:16.1.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\eddcfd92e1a5608e63c6ff0eb65c6ff4\transformed\expo.modules.camera-16.1.10\AndroidManifest.xml:13:13-39
117
118        <provider
118-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
119            android:name="expo.modules.filesystem.FileSystemFileProvider"
119-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
120            android:authorities="com.kingsanu.captainapp.FileSystemFileProvider"
120-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
121            android:exported="false"
121-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
122            android:grantUriPermissions="true" >
122-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
123            <meta-data
123-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
124                android:name="android.support.FILE_PROVIDER_PATHS"
124-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
125                android:resource="@xml/file_system_provider_paths" />
125-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
126        </provider>
127
128        <uses-library
128-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4093479fe42157f0f55fead2fd8228c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
129            android:name="androidx.camera.extensions.impl"
129-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4093479fe42157f0f55fead2fd8228c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
130            android:required="false" />
130-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4093479fe42157f0f55fead2fd8228c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
131
132        <service
132-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c3b0b4a57657654bda85ea0ad66089d\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
133            android:name="androidx.camera.core.impl.MetadataHolderService"
133-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c3b0b4a57657654bda85ea0ad66089d\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
134            android:enabled="false"
134-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c3b0b4a57657654bda85ea0ad66089d\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
135            android:exported="false" >
135-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c3b0b4a57657654bda85ea0ad66089d\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
136            <meta-data
136-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c3b0b4a57657654bda85ea0ad66089d\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
137                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
137-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c3b0b4a57657654bda85ea0ad66089d\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
138                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
138-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c3b0b4a57657654bda85ea0ad66089d\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
139        </service>
140        <!--
141        This activity is an invisible delegate activity to start scanner activity
142        and receive result, so it's unnecessary to support screen orientation and
143        we can avoid any side effect from activity recreation in any case.
144        -->
145        <activity
145-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
146            android:name="com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity"
146-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
147            android:exported="false"
147-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
148            android:screenOrientation="portrait" >
148-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
149        </activity>
150
151        <service
151-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
152            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
152-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
153            android:directBootAware="true"
153-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
154            android:exported="false" >
154-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
155            <meta-data
155-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
156                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
156-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
158            <meta-data
158-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\136f34202ce7744e681e139afd02dc79\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
159                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
159-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\136f34202ce7744e681e139afd02dc79\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\136f34202ce7744e681e139afd02dc79\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
161            <meta-data
161-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
162                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
162-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
164        </service>
165
166        <provider
166-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
167            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
167-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
168            android:authorities="com.kingsanu.captainapp.mlkitinitprovider"
168-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
169            android:exported="false"
169-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
170            android:initOrder="99" />
170-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
171
172        <meta-data
172-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea759b4c200c5e1eb197cb4831c5d9c1\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
173            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
173-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea759b4c200c5e1eb197cb4831c5d9c1\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
174            android:value="GlideModule" />
174-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea759b4c200c5e1eb197cb4831c5d9c1\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
175
176        <activity
176-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
177            android:name="com.google.android.gms.common.api.GoogleApiActivity"
177-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
178            android:exported="false"
178-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
179            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
179-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
180
181        <meta-data
181-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c47125c9a335e989accf2286b6eb952\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
182            android:name="com.google.android.gms.version"
182-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c47125c9a335e989accf2286b6eb952\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
183            android:value="@integer/google_play_services_version" />
183-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c47125c9a335e989accf2286b6eb952\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
184
185        <provider
185-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
186            android:name="androidx.startup.InitializationProvider"
186-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
187            android:authorities="com.kingsanu.captainapp.androidx-startup"
187-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
188            android:exported="false" >
188-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
189            <meta-data
189-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
190                android:name="androidx.emoji2.text.EmojiCompatInitializer"
190-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
191                android:value="androidx.startup" />
191-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
192            <meta-data
192-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a60d671dd3aa291ecde7f9adf86d9123\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
193                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
193-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a60d671dd3aa291ecde7f9adf86d9123\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
194                android:value="androidx.startup" />
194-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a60d671dd3aa291ecde7f9adf86d9123\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
195            <meta-data
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
196                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
197                android:value="androidx.startup" />
197-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
198        </provider>
199
200        <receiver
200-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
201            android:name="androidx.profileinstaller.ProfileInstallReceiver"
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
202            android:directBootAware="false"
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
203            android:enabled="true"
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
204            android:exported="true"
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
205            android:permission="android.permission.DUMP" >
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
206            <intent-filter>
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
207                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
208            </intent-filter>
209            <intent-filter>
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
210                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
211            </intent-filter>
212            <intent-filter>
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
213                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
213-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
213-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
214            </intent-filter>
215            <intent-filter>
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
216                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
217            </intent-filter>
218        </receiver>
219
220        <service
220-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91bd48e03fe9ed1ce8f2f6143ce792c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
221            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
221-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91bd48e03fe9ed1ce8f2f6143ce792c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
222            android:exported="false" >
222-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91bd48e03fe9ed1ce8f2f6143ce792c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
223            <meta-data
223-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91bd48e03fe9ed1ce8f2f6143ce792c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
224                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
224-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91bd48e03fe9ed1ce8f2f6143ce792c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
225                android:value="cct" />
225-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91bd48e03fe9ed1ce8f2f6143ce792c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
226        </service>
227        <service
227-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8cc87342e9ebeb58fc2b61862481a94\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
228            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
228-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8cc87342e9ebeb58fc2b61862481a94\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
229            android:exported="false"
229-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8cc87342e9ebeb58fc2b61862481a94\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
230            android:permission="android.permission.BIND_JOB_SERVICE" >
230-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8cc87342e9ebeb58fc2b61862481a94\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
231        </service>
232
233        <receiver
233-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8cc87342e9ebeb58fc2b61862481a94\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
234            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
234-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8cc87342e9ebeb58fc2b61862481a94\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
235            android:exported="false" />
235-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8cc87342e9ebeb58fc2b61862481a94\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
236    </application>
237
238</manifest>
