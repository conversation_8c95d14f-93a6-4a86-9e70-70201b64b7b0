1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.kingsanu.captainapp"
4    android:versionCode="2"
5    android:versionName="2.0.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:2:3-64
11-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:2:20-62
12    <uses-permission
12-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:3:3-77
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:3:20-75
14        android:maxSdkVersion="32" />
14-->[BareExpo:expo.modules.image:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3436d2bb0e66cba9430428ecbc7fb171\transformed\expo.modules.image-2.3.0\AndroidManifest.xml:17:9-35
15    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
15-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:4:3-75
15-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:4:20-73
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:5:3-63
16-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:5:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:6:3-78
17-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:6:20-76
18
19    <queries>
19-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:7:3-13:13
20        <intent>
20-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:8:5-12:14
21            <action android:name="android.intent.action.VIEW" />
21-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:9:7-58
21-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:9:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:10:7-67
23-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:10:17-65
24
25            <data android:scheme="https" />
25-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:11:7-37
25-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:11:13-35
26        </intent>
27        <!-- Query open documents -->
28        <intent>
28-->[:expo-file-system] K:\2025\captainapp_native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
29            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
29-->[:expo-file-system] K:\2025\captainapp_native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
29-->[:expo-file-system] K:\2025\captainapp_native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
30        </intent>
31        <intent>
31-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\424805646d63ef3498640e58b914c10d\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:8:9-12:18
32
33            <!-- Required for opening tabs if targeting API 30 -->
34            <action android:name="android.support.customtabs.action.CustomTabsService" />
34-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\424805646d63ef3498640e58b914c10d\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:13-90
34-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\424805646d63ef3498640e58b914c10d\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:21-87
35        </intent>
36    </queries>
37    <!--
38  Allows Glide to monitor connectivity status and restart failed requests if users go from a
39  a disconnected to a connected network state.
40    -->
41    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
41-->[BareExpo:expo.modules.image:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3436d2bb0e66cba9430428ecbc7fb171\transformed\expo.modules.image-2.3.0\AndroidManifest.xml:12:5-79
41-->[BareExpo:expo.modules.image:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3436d2bb0e66cba9430428ecbc7fb171\transformed\expo.modules.image-2.3.0\AndroidManifest.xml:12:22-76
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ad4bdeddd79d498a0e9dfe4af5a7d2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.kingsanu.captainapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ad4bdeddd79d498a0e9dfe4af5a7d2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ad4bdeddd79d498a0e9dfe4af5a7d2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.kingsanu.captainapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ad4bdeddd79d498a0e9dfe4af5a7d2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ad4bdeddd79d498a0e9dfe4af5a7d2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
49-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:14:3-30:17
50        android:name="com.kingsanu.captainapp.MainApplication"
50-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:14:16-47
51        android:allowBackup="true"
51-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:14:223-249
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ad4bdeddd79d498a0e9dfe4af5a7d2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
53        android:extractNativeLibs="false"
54        android:icon="@mipmap/ic_launcher"
54-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:14:81-115
55        android:label="@string/app_name"
55-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:14:48-80
56        android:networkSecurityConfig="@xml/network_security_config"
56-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:14:116-176
57        android:roundIcon="@mipmap/ic_launcher_round"
57-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:14:177-222
58        android:supportsRtl="true"
58-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:14:282-308
59        android:theme="@style/AppTheme" >
59-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:14:250-281
60        <meta-data
60-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:15:5-83
61            android:name="expo.modules.updates.ENABLED"
61-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:15:16-59
62            android:value="false" />
62-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:15:60-81
63        <meta-data
63-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:5-105
64            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
64-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:16-80
65            android:value="ALWAYS" />
65-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:16:81-103
66        <meta-data
66-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:17:5-99
67            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
67-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:17:16-79
68            android:value="0" />
68-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:17:80-97
69
70        <activity
70-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:18:5-29:16
71            android:name="com.kingsanu.captainapp.MainActivity"
71-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:18:15-43
72            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
72-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:18:44-134
73            android:exported="true"
73-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:18:256-279
74            android:launchMode="singleTask"
74-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:18:135-166
75            android:screenOrientation="portrait"
75-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:18:280-316
76            android:theme="@style/Theme.App.SplashScreen"
76-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:18:210-255
77            android:windowSoftInputMode="adjustResize" >
77-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:18:167-209
78            <intent-filter>
78-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:19:7-22:23
79                <action android:name="android.intent.action.MAIN" />
79-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:20:9-60
79-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:20:17-58
80
81                <category android:name="android.intent.category.LAUNCHER" />
81-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:21:9-68
81-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:21:19-66
82            </intent-filter>
83            <intent-filter>
83-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:23:7-28:23
84                <action android:name="android.intent.action.VIEW" />
84-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:9:7-58
84-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:9:15-56
85
86                <category android:name="android.intent.category.DEFAULT" />
86-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:25:9-67
86-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:25:19-65
87                <category android:name="android.intent.category.BROWSABLE" />
87-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:10:7-67
87-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:10:17-65
88
89                <data android:scheme="captainapp" />
89-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:11:7-37
89-->K:\2025\captainapp_native\android\app\src\main\AndroidManifest.xml:11:13-35
90            </intent-filter>
91        </activity>
92
93        <provider
93-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
94            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
94-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-83
95            android:authorities="com.kingsanu.captainapp.fileprovider"
95-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
96            android:exported="false"
96-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
97            android:grantUriPermissions="true" >
97-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
98            <meta-data
98-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
99                android:name="android.support.FILE_PROVIDER_PATHS"
99-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
100                android:resource="@xml/file_provider_paths" />
100-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
101        </provider>
102
103        <meta-data
103-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
104            android:name="org.unimodules.core.AppLoader#react-native-headless"
104-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
105            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
105-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
106        <meta-data
106-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
107            android:name="com.facebook.soloader.enabled"
107-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
108            android:value="true" />
108-->[:expo-modules-core] K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
109
110        <provider
110-->[:expo-file-system] K:\2025\captainapp_native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
111            android:name="expo.modules.filesystem.FileSystemFileProvider"
111-->[:expo-file-system] K:\2025\captainapp_native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
112            android:authorities="com.kingsanu.captainapp.FileSystemFileProvider"
112-->[:expo-file-system] K:\2025\captainapp_native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
113            android:exported="false"
113-->[:expo-file-system] K:\2025\captainapp_native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
114            android:grantUriPermissions="true" >
114-->[:expo-file-system] K:\2025\captainapp_native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
115            <meta-data
115-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
116                android:name="android.support.FILE_PROVIDER_PATHS"
116-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
117                android:resource="@xml/file_system_provider_paths" />
117-->[:react-native-webview] K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
118        </provider>
119
120        <meta-data
120-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c2c99566bca8247160f1fa1ef8c1cc\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
121            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
121-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c2c99566bca8247160f1fa1ef8c1cc\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
122            android:value="GlideModule" />
122-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c2c99566bca8247160f1fa1ef8c1cc\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
123
124        <provider
124-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdcbec29e8d0b717843f3dbae0b94f84\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
125            android:name="androidx.startup.InitializationProvider"
125-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdcbec29e8d0b717843f3dbae0b94f84\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
126            android:authorities="com.kingsanu.captainapp.androidx-startup"
126-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdcbec29e8d0b717843f3dbae0b94f84\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
127            android:exported="false" >
127-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdcbec29e8d0b717843f3dbae0b94f84\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
128            <meta-data
128-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdcbec29e8d0b717843f3dbae0b94f84\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
129                android:name="androidx.emoji2.text.EmojiCompatInitializer"
129-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdcbec29e8d0b717843f3dbae0b94f84\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
130                android:value="androidx.startup" />
130-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdcbec29e8d0b717843f3dbae0b94f84\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
131            <meta-data
131-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3f62d4745054bc29342c5607bcde4d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
132                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
132-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3f62d4745054bc29342c5607bcde4d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
133                android:value="androidx.startup" />
133-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3f62d4745054bc29342c5607bcde4d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
134            <meta-data
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
135                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
136                android:value="androidx.startup" />
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
137        </provider>
138
139        <receiver
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
140            android:name="androidx.profileinstaller.ProfileInstallReceiver"
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
141            android:directBootAware="false"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
142            android:enabled="true"
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
143            android:exported="true"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
144            android:permission="android.permission.DUMP" >
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
145            <intent-filter>
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
146                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
147            </intent-filter>
148            <intent-filter>
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
149                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
150            </intent-filter>
151            <intent-filter>
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
152                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
153            </intent-filter>
154            <intent-filter>
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
155                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
156            </intent-filter>
157        </receiver>
158    </application>
159
160</manifest>
