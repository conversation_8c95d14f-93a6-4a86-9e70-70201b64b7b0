com.kingsanu.captainapp-camera-core-1.4.1-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\000c091bd04d9332c75e921a42268f08\transformed\camera-core-1.4.1\res
com.kingsanu.captainapp-tracing-ktx-1.2.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\05c532ea6eeed5ba96fff64378302997\transformed\tracing-ktx-1.2.0\res
com.kingsanu.captainapp-appcompat-resources-1.7.0-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\067eb4768d0977cc253e099ab589c211\transformed\appcompat-resources-1.7.0\res
com.kingsanu.captainapp-activity-ktx-1.8.0-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\0b3cd90608d731515ee5b0c8986ce7d6\transformed\activity-ktx-1.8.0\res
com.kingsanu.captainapp-lifecycle-livedata-core-ktx-2.6.2-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\0e45932910c15ea66169daadac963681\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.kingsanu.captainapp-avif-3.0.3-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\110f776d668ac689e1f94b3d644b3745\transformed\avif-3.0.3\res
com.kingsanu.captainapp-startup-runtime-1.1.1-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\12f7ec3034c44ad7915028fd2e88ba60\transformed\startup-runtime-1.1.1\res
com.kingsanu.captainapp-constraintlayout-2.0.1-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\162c641a6ded6de9dcbbe71bb887fa9c\transformed\constraintlayout-2.0.1\res
com.kingsanu.captainapp-savedstate-1.2.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\19f8795450958262f34e41d8151462d1\transformed\savedstate-1.2.1\res
com.kingsanu.captainapp-fragment-1.6.1-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\22d97c11c7e69f31bac4e14a1df1d05a\transformed\fragment-1.6.1\res
com.kingsanu.captainapp-lifecycle-runtime-ktx-2.6.2-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\2af3bcc447ac110862396e7d0b0ec9ae\transformed\lifecycle-runtime-ktx-2.6.2\res
com.kingsanu.captainapp-recyclerview-1.1.0-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\2db4240268c509e9b190ca1a393ca7d5\transformed\recyclerview-1.1.0\res
com.kingsanu.captainapp-drawerlayout-1.1.1-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\34bb67911f8414f1ff69475eb4175853\transformed\drawerlayout-1.1.1\res
com.kingsanu.captainapp-expo.modules.filesystem-18.1.11-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\res
com.kingsanu.captainapp-apng-3.0.3-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\36a6efa12988c187e949b2792748cc83\transformed\apng-3.0.3\res
com.kingsanu.captainapp-core-runtime-2.2.0-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4cadc5d581274a383a17f34b96bacf\transformed\core-runtime-2.2.0\res
com.kingsanu.captainapp-camera-camera2-1.4.1-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\3c3b0b4a57657654bda85ea0ad66089d\transformed\camera-camera2-1.4.1\res
com.kingsanu.captainapp-expo.modules.splashscreen-0.30.9-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\3ca523eae88a74dbca6d2990aa18fb74\transformed\expo.modules.splashscreen-0.30.9\res
com.kingsanu.captainapp-glide-plugin-3.0.3-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\3cae8affa140af3cbca690147b5ea5d0\transformed\glide-plugin-3.0.3\res
com.kingsanu.captainapp-lifecycle-viewmodel-2.6.2-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\44911df0b7c9625b5575bd1446144add\transformed\lifecycle-viewmodel-2.6.2\res
com.kingsanu.captainapp-profileinstaller-1.3.1-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\res
com.kingsanu.captainapp-camera-view-1.4.1-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\475bbefeaf2d9a702d35505e832753aa\transformed\camera-view-1.4.1\res
com.kingsanu.captainapp-camera-video-1.4.1-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\49f0ca8937cfac714a379a0e7cb32d56\transformed\camera-video-1.4.1\res
com.kingsanu.captainapp-core-ktx-1.13.1-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\4bae3fed21d5f02d6bf43c03532e29f6\transformed\core-ktx-1.13.1\res
com.kingsanu.captainapp-fragment-ktx-1.6.1-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\506e255793c8bac2342ad449777733c1\transformed\fragment-ktx-1.6.1\res
com.kingsanu.captainapp-autofill-1.1.0-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\50fb524026069718687c4ab652c29053\transformed\autofill-1.1.0\res
com.kingsanu.captainapp-lifecycle-runtime-2.6.2-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\5379c5771c0cc88a961c78af9bb09339\transformed\lifecycle-runtime-2.6.2\res
com.kingsanu.captainapp-cardview-1.0.0-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\5745f858b74ff0a2f8e17d4b26e19812\transformed\cardview-1.0.0\res
com.kingsanu.captainapp-emoji2-1.3.0-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\res
com.kingsanu.captainapp-gif-3.0.3-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\5dacd14eb7936ae24dc23c074013c5c9\transformed\gif-3.0.3\res
com.kingsanu.captainapp-tracing-1.2.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\5e0f9e6bf46b1bd6aeea3c4a7ba42299\transformed\tracing-1.2.0\res
com.kingsanu.captainapp-media-1.0.0-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\6183b82bc0c6a1834671e2c74c065f3b\transformed\media-1.0.0\res
com.kingsanu.captainapp-viewpager2-1.0.0-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\65ba2f9fa3c9fe83e5152e0f7195116c\transformed\viewpager2-1.0.0\res
com.kingsanu.captainapp-core-1.13.1-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\res
com.kingsanu.captainapp-savedstate-ktx-1.2.1-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\6d8784d24425b9166c30f1a4f5571e15\transformed\savedstate-ktx-1.2.1\res
com.kingsanu.captainapp-material-1.12.0-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\7becb12098085a58be85c10a694bf84d\transformed\material-1.12.0\res
com.kingsanu.captainapp-BlurView-version-2.0.6-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\87e4c57220eac6c2a196044df99f723d\transformed\BlurView-version-2.0.6\res
com.kingsanu.captainapp-play-services-basement-18.4.0-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\8c47125c9a335e989accf2286b6eb952\transformed\play-services-basement-18.4.0\res
com.kingsanu.captainapp-lifecycle-livedata-core-2.6.2-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\8fbfdc943d04e82c10add9c26833a5c6\transformed\lifecycle-livedata-core-2.6.2\res
com.kingsanu.captainapp-drawee-3.6.0-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\90554224eff49d6253bc7f46090a200f\transformed\drawee-3.6.0\res
com.kingsanu.captainapp-transition-1.5.0-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\925e579c481c06349ca2c1bdadbdec35\transformed\transition-1.5.0\res
com.kingsanu.captainapp-annotation-experimental-1.4.1-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\9478ca0554caeea9ae571964e0cb2ca6\transformed\annotation-experimental-1.4.1\res
com.kingsanu.captainapp-coordinatorlayout-1.2.0-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\94f09ae075da36273dd5b0f48955e034\transformed\coordinatorlayout-1.2.0\res
com.kingsanu.captainapp-lifecycle-viewmodel-ktx-2.6.2-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\98eec38dab2893f9dde1642e7a91ed70\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.kingsanu.captainapp-lifecycle-process-2.6.2-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\a60d671dd3aa291ecde7f9adf86d9123\transformed\lifecycle-process-2.6.2\res
com.kingsanu.captainapp-core-splashscreen-1.2.0-alpha02-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\aa71ddfffa468141c4e43e0245303cf6\transformed\core-splashscreen-1.2.0-alpha02\res
com.kingsanu.captainapp-androidsvg-aar-1.4-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\ac9879374d2e96acc4c0b73efbd01c6c\transformed\androidsvg-aar-1.4\res
com.kingsanu.captainapp-appcompat-1.7.0-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\af9a9d7ace362c65c6063a55648731b1\transformed\appcompat-1.7.0\res
com.kingsanu.captainapp-glide-4.16.0-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\bbeeba403c008876f9fc01e5e82c994b\transformed\glide-4.16.0\res
com.kingsanu.captainapp-play-services-base-18.5.0-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\res
com.kingsanu.captainapp-react-android-0.79.5-release-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\c0eaab12c39868587e4f18db7dfb16a1\transformed\react-android-0.79.5-release\res
com.kingsanu.captainapp-swiperefreshlayout-1.1.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\c3179e0f1b67cb33410fa674edb5496c\transformed\swiperefreshlayout-1.1.0\res
com.kingsanu.captainapp-lifecycle-viewmodel-savedstate-2.6.2-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\c4b770dc8989251f37e56402d8aa1685\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.kingsanu.captainapp-browser-1.6.0-53 C:\Users\<USER>\.gradle\caches\8.13\transforms\cd10ac37c6d4674d33088c916aa639d9\transformed\browser-1.6.0\res
com.kingsanu.captainapp-activity-1.8.0-54 C:\Users\<USER>\.gradle\caches\8.13\transforms\ce37a28011a1b60b4e3d132dbd8b1975\transformed\activity-1.8.0\res
com.kingsanu.captainapp-awebp-3.0.3-55 C:\Users\<USER>\.gradle\caches\8.13\transforms\df98dc24e95513b01b950af52f1666a4\transformed\awebp-3.0.3\res
com.kingsanu.captainapp-camera-mlkit-vision-1.4.1-56 C:\Users\<USER>\.gradle\caches\8.13\transforms\e6cd85f320ba49c2086646f9813a1a92\transformed\camera-mlkit-vision-1.4.1\res
com.kingsanu.captainapp-emoji2-views-helper-1.3.0-57 C:\Users\<USER>\.gradle\caches\8.13\transforms\ec1853d0b0976e32a4f952e316c0c604\transformed\emoji2-views-helper-1.3.0\res
com.kingsanu.captainapp-frameanimation-3.0.3-58 C:\Users\<USER>\.gradle\caches\8.13\transforms\ed93576eee364c6358f1a6a94d6f0872\transformed\frameanimation-3.0.3\res
com.kingsanu.captainapp-camera-extensions-1.4.1-59 C:\Users\<USER>\.gradle\caches\8.13\transforms\f4093479fe42157f0f55fead2fd8228c\transformed\camera-extensions-1.4.1\res
com.kingsanu.captainapp-camera-lifecycle-1.4.1-60 C:\Users\<USER>\.gradle\caches\8.13\transforms\f4cb95a8d412314cbef6bf8e0afa9ec6\transformed\camera-lifecycle-1.4.1\res
com.kingsanu.captainapp-lifecycle-livedata-2.6.2-61 C:\Users\<USER>\.gradle\caches\8.13\transforms\fa08d204398219a1a89b242b31fa3041\transformed\lifecycle-livedata-2.6.2\res
com.kingsanu.captainapp-expo.modules.systemui-5.0.8-62 C:\Users\<USER>\.gradle\caches\8.13\transforms\fc9770245cca31a26e7843d26f3274ad\transformed\expo.modules.systemui-5.0.8\res
com.kingsanu.captainapp-res-63 K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets
com.kingsanu.captainapp-pngs-64 K:\2025\captainapp_native\android\app\build\generated\res\pngs\release
com.kingsanu.captainapp-resValues-65 K:\2025\captainapp_native\android\app\build\generated\res\resValues\release
com.kingsanu.captainapp-packageReleaseResources-66 K:\2025\captainapp_native\android\app\build\intermediates\incremental\release\packageReleaseResources\merged.dir
com.kingsanu.captainapp-packageReleaseResources-67 K:\2025\captainapp_native\android\app\build\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.kingsanu.captainapp-release-68 K:\2025\captainapp_native\android\app\build\intermediates\merged_res\release\mergeReleaseResources
com.kingsanu.captainapp-main-69 K:\2025\captainapp_native\android\app\src\main\res
com.kingsanu.captainapp-release-70 K:\2025\captainapp_native\android\app\src\release\res
com.kingsanu.captainapp-release-71 K:\2025\captainapp_native\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-72 K:\2025\captainapp_native\node_modules\expo-barcode-scanner\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-73 K:\2025\captainapp_native\node_modules\expo-constants\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-74 K:\2025\captainapp_native\node_modules\expo-image-loader\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-75 K:\2025\captainapp_native\node_modules\expo-linking\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-76 K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-77 K:\2025\captainapp_native\node_modules\expo\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-78 K:\2025\captainapp_native\node_modules\react-native-edge-to-edge\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-79 K:\2025\captainapp_native\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-80 K:\2025\captainapp_native\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-81 K:\2025\captainapp_native\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-82 K:\2025\captainapp_native\node_modules\react-native-screens\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-83 K:\2025\captainapp_native\node_modules\react-native-svg\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-84 K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\packaged_res\release\packageReleaseResources
