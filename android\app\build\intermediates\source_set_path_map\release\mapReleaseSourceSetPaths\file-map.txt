com.kingsanu.captainapp-viewpager2-1.0.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\0a985a726a0ed91053e59647f893676a\transformed\viewpager2-1.0.0\res
com.kingsanu.captainapp-savedstate-1.2.1-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\0b7ca141aa9d3a05c286852a98884a1f\transformed\savedstate-1.2.1\res
com.kingsanu.captainapp-androidsvg-aar-1.4-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\0ced2c618cc0851d3ee11456ab9ae13d\transformed\androidsvg-aar-1.4\res
com.kingsanu.captainapp-appcompat-resources-1.7.0-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\0ea8215bc09ef74c628f4e4a0402513e\transformed\appcompat-resources-1.7.0\res
com.kingsanu.captainapp-expo.modules.systemui-5.0.8-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\107aba075f519d3df9f27b8031d582e4\transformed\expo.modules.systemui-5.0.8\res
com.kingsanu.captainapp-lifecycle-livedata-core-ktx-2.6.2-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\10b213ecea0e4e52da532b6ba0f83370\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.kingsanu.captainapp-material-1.12.0-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\1688e4f4998421f6cdbc59f17cef2f61\transformed\material-1.12.0\res
com.kingsanu.captainapp-media-1.0.0-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\17e95e9d548b080f2342fe4e9d66b27c\transformed\media-1.0.0\res
com.kingsanu.captainapp-tracing-ktx-1.2.0-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\1bbf8401d34e5d62926e60ab182c1f96\transformed\tracing-ktx-1.2.0\res
com.kingsanu.captainapp-appcompat-1.7.0-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\1cc5e9e2cf9bf5411b10463ffe5bdf1d\transformed\appcompat-1.7.0\res
com.kingsanu.captainapp-core-1.13.1-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\25ad4bdeddd79d498a0e9dfe4af5a7d2\transformed\core-1.13.1\res
com.kingsanu.captainapp-drawee-3.6.0-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\2f42596c4ca5d2009892461f9c7813b2\transformed\drawee-3.6.0\res
com.kingsanu.captainapp-profileinstaller-1.3.1-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\res
com.kingsanu.captainapp-core-ktx-1.13.1-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\44982b7d7b05239c12187164d2522afe\transformed\core-ktx-1.13.1\res
com.kingsanu.captainapp-browser-1.6.0-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\44d5193287b18dec49d9cd0bdd73b6e9\transformed\browser-1.6.0\res
com.kingsanu.captainapp-fragment-ktx-1.6.1-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\45a5fcb4b05df7123a46aa86205607f0\transformed\fragment-ktx-1.6.1\res
com.kingsanu.captainapp-recyclerview-1.1.0-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\47a56a4f1be0e9a7d815855bffa6881d\transformed\recyclerview-1.1.0\res
com.kingsanu.captainapp-constraintlayout-2.0.1-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\4a6ff7a886435c160fc114598df514e8\transformed\constraintlayout-2.0.1\res
com.kingsanu.captainapp-react-android-0.79.4-release-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\55e5e2b47fc8f51b895ce8757651a2de\transformed\react-android-0.79.4-release\res
com.kingsanu.captainapp-cardview-1.0.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\57b0ed4888c5d35f21d94e7e07d01862\transformed\cardview-1.0.0\res
com.kingsanu.captainapp-autofill-1.1.0-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\6126635189ee6e9915c55ed159ffcefd\transformed\autofill-1.1.0\res
com.kingsanu.captainapp-coordinatorlayout-1.2.0-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\675643d2417f53f62de6e71cb70c4a03\transformed\coordinatorlayout-1.2.0\res
com.kingsanu.captainapp-BlurView-version-2.0.6-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\67a76227458514088161a2b0747e4617\transformed\BlurView-version-2.0.6\res
com.kingsanu.captainapp-lifecycle-viewmodel-ktx-2.6.2-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\6a7fec56ee66dd7f7a0a2b21f81fa72c\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.kingsanu.captainapp-glide-4.16.0-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4036466a3da156a07d7c15151d5f6a\transformed\glide-4.16.0\res
com.kingsanu.captainapp-lifecycle-livedata-core-2.6.2-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\74c5ef7ab2dc463f805db9e9370b6b3a\transformed\lifecycle-livedata-core-2.6.2\res
com.kingsanu.captainapp-frameanimation-3.0.3-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\76f79e0c31791936fb2bd92707f6357d\transformed\frameanimation-3.0.3\res
com.kingsanu.captainapp-fragment-1.6.1-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\7cf11ab6a97ce7a19d873c7fc9f6245f\transformed\fragment-1.6.1\res
com.kingsanu.captainapp-activity-ktx-1.8.0-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\7e03b29ff010dcda13836cde37647491\transformed\activity-ktx-1.8.0\res
com.kingsanu.captainapp-awebp-3.0.3-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\7ee6ee78b8b7385b6567f6e3572e56c2\transformed\awebp-3.0.3\res
com.kingsanu.captainapp-emoji2-views-helper-1.3.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\8109800e0431b448d21295476395ef1d\transformed\emoji2-views-helper-1.3.0\res
com.kingsanu.captainapp-savedstate-ktx-1.2.1-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\86cbd6534acf09b6edb34f1ee4cf1ea6\transformed\savedstate-ktx-1.2.1\res
com.kingsanu.captainapp-activity-1.8.0-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\8f54346d7887f87b0ac14941a641e1e5\transformed\activity-1.8.0\res
com.kingsanu.captainapp-annotation-experimental-1.4.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\9a3668def9ff4e9f17f553dcb425dac8\transformed\annotation-experimental-1.4.0\res
com.kingsanu.captainapp-swiperefreshlayout-1.1.0-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\a803a10f06d03b85c791e9e7827df3a7\transformed\swiperefreshlayout-1.1.0\res
com.kingsanu.captainapp-startup-runtime-1.1.1-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\b02b404f32dd5de34e82e545d50d8192\transformed\startup-runtime-1.1.1\res
com.kingsanu.captainapp-expo.modules.splashscreen-0.30.9-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\b3390bd7dd069ce47ea2215a9701dc04\transformed\expo.modules.splashscreen-0.30.9\res
com.kingsanu.captainapp-lifecycle-viewmodel-savedstate-2.6.2-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\b6d7366c2eeb6fe782cd6baab9130943\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.kingsanu.captainapp-lifecycle-viewmodel-2.6.2-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\bae0062137d08dab4b3c13dc4624a335\transformed\lifecycle-viewmodel-2.6.2\res
com.kingsanu.captainapp-transition-1.5.0-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\bdbe8332f585138dd54b686bb64a82aa\transformed\transition-1.5.0\res
com.kingsanu.captainapp-emoji2-1.3.0-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\bdcbec29e8d0b717843f3dbae0b94f84\transformed\emoji2-1.3.0\res
com.kingsanu.captainapp-lifecycle-livedata-2.6.2-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\c0f61044d9766c534145df491b529aab\transformed\lifecycle-livedata-2.6.2\res
com.kingsanu.captainapp-apng-3.0.3-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\e02d6619476aba0105310be2159d96f9\transformed\apng-3.0.3\res
com.kingsanu.captainapp-core-splashscreen-1.2.0-alpha02-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\e03d0108970ad49120068551e56f0044\transformed\core-splashscreen-1.2.0-alpha02\res
com.kingsanu.captainapp-gif-3.0.3-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\ea85dbefea5c1958d4cdd80a099d89cf\transformed\gif-3.0.3\res
com.kingsanu.captainapp-drawerlayout-1.1.1-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\eabbbcce580f93f46e8da890fc2dc6b7\transformed\drawerlayout-1.1.1\res
com.kingsanu.captainapp-tracing-1.2.0-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\eb45ff10c0db144c5a5537429e93efe3\transformed\tracing-1.2.0\res
com.kingsanu.captainapp-lifecycle-process-2.6.2-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3f62d4745054bc29342c5607bcde4d\transformed\lifecycle-process-2.6.2\res
com.kingsanu.captainapp-glide-plugin-3.0.3-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\f40077589a7ac8623ba297258e75d28b\transformed\glide-plugin-3.0.3\res
com.kingsanu.captainapp-avif-3.0.3-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\fa93cb06fb117b034e9e00e2254e875d\transformed\avif-3.0.3\res
com.kingsanu.captainapp-lifecycle-runtime-2.6.2-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\fb72dc500fcacad8fdd089fb6045d7e1\transformed\lifecycle-runtime-2.6.2\res
com.kingsanu.captainapp-core-runtime-2.2.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\fcdbde368ff3f7b130f131373693d72e\transformed\core-runtime-2.2.0\res
com.kingsanu.captainapp-lifecycle-runtime-ktx-2.6.2-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\fece13e9c08c13338369de69cd14e88c\transformed\lifecycle-runtime-ktx-2.6.2\res
com.kingsanu.captainapp-res-53 K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets
com.kingsanu.captainapp-pngs-54 K:\2025\captainapp_native\android\app\build\generated\res\pngs\release
com.kingsanu.captainapp-resValues-55 K:\2025\captainapp_native\android\app\build\generated\res\resValues\release
com.kingsanu.captainapp-packageReleaseResources-56 K:\2025\captainapp_native\android\app\build\intermediates\incremental\release\packageReleaseResources\merged.dir
com.kingsanu.captainapp-packageReleaseResources-57 K:\2025\captainapp_native\android\app\build\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.kingsanu.captainapp-release-58 K:\2025\captainapp_native\android\app\build\intermediates\merged_res\release\mergeReleaseResources
com.kingsanu.captainapp-main-59 K:\2025\captainapp_native\android\app\src\main\res
com.kingsanu.captainapp-release-60 K:\2025\captainapp_native\android\app\src\release\res
com.kingsanu.captainapp-release-61 K:\2025\captainapp_native\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-62 K:\2025\captainapp_native\node_modules\expo-constants\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-63 K:\2025\captainapp_native\node_modules\expo-file-system\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-64 K:\2025\captainapp_native\node_modules\expo-linking\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-65 K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-66 K:\2025\captainapp_native\node_modules\expo\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-67 K:\2025\captainapp_native\node_modules\react-native-edge-to-edge\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-68 K:\2025\captainapp_native\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-69 K:\2025\captainapp_native\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-70 K:\2025\captainapp_native\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-71 K:\2025\captainapp_native\node_modules\react-native-screens\android\build\intermediates\packaged_res\release\packageReleaseResources
com.kingsanu.captainapp-release-72 K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\packaged_res\release\packageReleaseResources
