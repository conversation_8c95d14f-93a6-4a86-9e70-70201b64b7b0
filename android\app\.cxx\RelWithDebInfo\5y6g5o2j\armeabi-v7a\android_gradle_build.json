{"buildFiles": ["K:\\2025\\captainapp_native\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\captainapp_native\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\captainapp_native\\android\\app\\.cxx\\RelWithDebInfo\\5y6g5o2j\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\captainapp_native\\android\\app\\.cxx\\RelWithDebInfo\\5y6g5o2j\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "appmodules", "output": "K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\armeabi-v7a\\libappmodules.so", "runtimeFiles": ["K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f7366e53586a12172ab374397325a4bf\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNCWebViewSpec"}, "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNEdgeToEdge"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnscreens", "output": "K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f7366e53586a12172ab374397325a4bf\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_safeareacontext", "output": "K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5y6g5o2j\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f7366e53586a12172ab374397325a4bf\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}