<libraries>
  <library
      name=":@@:react-native-screens::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\872fdf52a6266e5530f156055d1e68fe\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\872fdf52a6266e5530f156055d1e68fe\transformed\out\jars\libs\R.jar"
      resolved="FoodyQueen Captain:react-native-screens:unspecified"
      partialResultsDir="K:\2025\captainapp_native\node_modules\react-native-screens\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\872fdf52a6266e5530f156055d1e68fe\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-edge-to-edge::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b832a724d36726915bf7eb9aee244157\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\b832a724d36726915bf7eb9aee244157\transformed\out\jars\libs\R.jar"
      resolved="FoodyQueen Captain:react-native-edge-to-edge:unspecified"
      partialResultsDir="K:\2025\captainapp_native\node_modules\react-native-edge-to-edge\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b832a724d36726915bf7eb9aee244157\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:react-android:0.79.4:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\55e5e2b47fc8f51b895ce8757651a2de\transformed\react-android-0.79.4-release\jars\classes.jar"
      resolved="com.facebook.react:react-android:0.79.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\55e5e2b47fc8f51b895ce8757651a2de\transformed\react-android-0.79.4-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fresco:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9ace98e86e8daf77252a03f707fe16e5\transformed\fresco-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:fresco:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9ace98e86e8daf77252a03f707fe16e5\transformed\fresco-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7c035c9f5baa4c873c913fa078c879e7\transformed\imagepipeline-okhttp3-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-okhttp3:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7c035c9f5baa4c873c913fa078c879e7\transformed\imagepipeline-okhttp3-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:middleware:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\afcf702965a22ec9b05ff95daaacf5ab\transformed\middleware-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:middleware:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\afcf702965a22ec9b05ff95daaacf5ab\transformed\middleware-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-common:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2072985832aa083bee5cf76d9f79c99b\transformed\ui-common-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-common:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2072985832aa083bee5cf76d9f79c99b\transformed\ui-common-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a5e3801483e4837a1d4fbbf604425fdb\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\a5e3801483e4837a1d4fbbf604425fdb\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo:53.0.12"
      partialResultsDir="K:\2025\captainapp_native\node_modules\expo\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a5e3801483e4837a1d4fbbf604425fdb\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-file-system::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\02aec13b4cd315bb9ba7812afee96eec\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\02aec13b4cd315bb9ba7812afee96eec\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-file-system:18.1.10"
      partialResultsDir="K:\2025\captainapp_native\node_modules\expo-file-system\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\02aec13b4cd315bb9ba7812afee96eec\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="BareExpo:expo.modules.image:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3436d2bb0e66cba9430428ecbc7fb171\transformed\expo.modules.image-2.3.0\jars\classes.jar"
      resolved="BareExpo:expo.modules.image:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3436d2bb0e66cba9430428ecbc7fb171\transformed\expo.modules.image-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp-urlconnection\4.9.2\3b9e64d3d56370bc7488ed8b336d17a8013cb336\okhttp-urlconnection-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-urlconnection:4.9.2"/>
  <library
      name="com.github.bumptech.glide:okhttp3-integration:4.11.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\20c2c99566bca8247160f1fa1ef8c1cc\transformed\okhttp3-integration-4.11.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:okhttp3-integration:4.11.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\20c2c99566bca8247160f1fa1ef8c1cc\transformed\okhttp3-integration-4.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.9.2\5302714ee9320b64cf65ed865e5f65981ef9ba46\okhttp-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp:4.9.2"/>
  <library
      name="com.squareup.okio:okio:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio\2.9.0\dcc813b08ce5933f8bdfd1dfbab4ad4bd170e7a\okio-jvm-2.9.0.jar"
      resolved="com.squareup.okio:okio:2.9.0"/>
  <library
      name=":@@:expo-modules-core::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c65b47c4d2127e40235e32e646c07696\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\c65b47c4d2127e40235e32e646c07696\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-modules-core:2.4.0"
      partialResultsDir="K:\2025\captainapp_native\node_modules\expo-modules-core\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c65b47c4d2127e40235e32e646c07696\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0ea8215bc09ef74c628f4e4a0402513e\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0ea8215bc09ef74c628f4e4a0402513e\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1cc5e9e2cf9bf5411b10463ffe5bdf1d\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1cc5e9e2cf9bf5411b10463ffe5bdf1d\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\efa3c0a2e02549c1d639d2cb5cf43903\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\efa3c0a2e02549c1d639d2cb5cf43903\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4036466a3da156a07d7c15151d5f6a\transformed\glide-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4036466a3da156a07d7c15151d5f6a\transformed\glide-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7cf11ab6a97ce7a19d873c7fc9f6245f\transformed\fragment-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7cf11ab6a97ce7a19d873c7fc9f6245f\transformed\fragment-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8f54346d7887f87b0ac14941a641e1e5\transformed\activity-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8f54346d7887f87b0ac14941a641e1e5\transformed\activity-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7cbd0f7d2422a506820c11fbac42769d\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7cbd0f7d2422a506820c11fbac42769d\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a803a10f06d03b85c791e9e7827df3a7\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a803a10f06d03b85c791e9e7827df3a7\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.webbrowser:14.1.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\424805646d63ef3498640e58b914c10d\transformed\expo.modules.webbrowser-14.1.6\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.webbrowser:14.1.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\424805646d63ef3498640e58b914c10d\transformed\expo.modules.webbrowser-14.1.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\44d5193287b18dec49d9cd0bdd73b6e9\transformed\browser-1.6.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\44d5193287b18dec49d9cd0bdd73b6e9\transformed\browser-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\eabbbcce580f93f46e8da890fc2dc6b7\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\eabbbcce580f93f46e8da890fc2dc6b7\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\814e25e87c08337205c573e8fbde7912\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\814e25e87c08337205c573e8fbde7912\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d506d84aa1481d9d5e3867a8b5a87066\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d506d84aa1481d9d5e3867a8b5a87066\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\17e95e9d548b080f2342fe4e9d66b27c\transformed\media-1.0.0\jars\classes.jar"
      resolved="androidx.media:media:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\17e95e9d548b080f2342fe4e9d66b27c\transformed\media-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\919c29d3ded19ccee428cb72162a56a5\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\919c29d3ded19ccee428cb72162a56a5\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\19e333d2a1598d1596ab4253be890ecc\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\19e333d2a1598d1596ab4253be890ecc\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\675643d2417f53f62de6e71cb70c4a03\transformed\coordinatorlayout-1.2.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\675643d2417f53f62de6e71cb70c4a03\transformed\coordinatorlayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf22f6268219602ea588d58cca050520\transformed\slidingpanelayout-1.0.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf22f6268219602ea588d58cca050520\transformed\slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2876b5b071ba525c32ef673b01793303\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2876b5b071ba525c32ef673b01793303\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed80d3bf5b670828b2b3bc6f4b4dfeb\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed80d3bf5b670828b2b3bc6f4b4dfeb\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9fe9a9a5dc989223350b29a84763d980\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9fe9a9a5dc989223350b29a84763d980\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\25ad4bdeddd79d498a0e9dfe4af5a7d2\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\25ad4bdeddd79d498a0e9dfe4af5a7d2\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c0f61044d9766c534145df491b529aab\transformed\lifecycle-livedata-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c0f61044d9766c534145df491b529aab\transformed\lifecycle-livedata-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.2\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.2"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\74c5ef7ab2dc463f805db9e9370b6b3a\transformed\lifecycle-livedata-core-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\74c5ef7ab2dc463f805db9e9370b6b3a\transformed\lifecycle-livedata-core-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bae0062137d08dab4b3c13dc4624a335\transformed\lifecycle-viewmodel-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bae0062137d08dab4b3c13dc4624a335\transformed\lifecycle-viewmodel-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb72dc500fcacad8fdd089fb6045d7e1\transformed\lifecycle-runtime-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb72dc500fcacad8fdd089fb6045d7e1\transformed\lifecycle-runtime-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b6d7366c2eeb6fe782cd6baab9130943\transformed\lifecycle-viewmodel-savedstate-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b6d7366c2eeb6fe782cd6baab9130943\transformed\lifecycle-viewmodel-savedstate-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\44982b7d7b05239c12187164d2522afe\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\44982b7d7b05239c12187164d2522afe\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0b7ca141aa9d3a05c286852a98884a1f\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0b7ca141aa9d3a05c286852a98884a1f\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fbcore:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\805d64c1d3740b135c3c6d25d007a3e6\transformed\fbcore-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:fbcore:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\805d64c1d3740b135c3c6d25d007a3e6\transformed\fbcore-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:drawee:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2f42596c4ca5d2009892461f9c7813b2\transformed\drawee-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:drawee:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2f42596c4ca5d2009892461f9c7813b2\transformed\drawee-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name=":@@:expo-constants::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b42e426fb199e5845d9b1e94ba5a9a\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b42e426fb199e5845d9b1e94ba5a9a\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-constants:17.1.6"
      partialResultsDir="K:\2025\captainapp_native\node_modules\expo-constants\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b42e426fb199e5845d9b1e94ba5a9a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.haptics:14.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\45fbacacfd03779f88e5bdab927202e7\transformed\expo.modules.haptics-14.1.4\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.haptics:14.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\45fbacacfd03779f88e5bdab927202e7\transformed\expo.modules.haptics-14.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5c242fbce19fdaa380f4f5131b50e6e6\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5c242fbce19fdaa380f4f5131b50e6e6\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b0dc910de971c283f6356d856b6fba5e\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b0dc910de971c283f6356d856b6fba5e\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e78d9459b2e333fa78bced9c54bd129f\transformed\gifdecoder-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e78d9459b2e333fa78bced9c54bd129f\transformed\gifdecoder-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\be6898bf4d583734f3f30b86f3aa7db4\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\be6898bf4d583734f3f30b86f3aa7db4\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7c602fb14d62922ac8d7f7c4ab0d51ca\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7c602fb14d62922ac8d7f7c4ab0d51ca\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\efff7a0c1ff905ae9950c94d7858e693\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\efff7a0c1ff905ae9950c94d7858e693\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\adf446957242012738eebe9b97a609ca\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\adf446957242012738eebe9b97a609ca\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fcdbde368ff3f7b130f131373693d72e\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fcdbde368ff3f7b130f131373693d72e\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9a3668def9ff4e9f17f553dcb425dac8\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9a3668def9ff4e9f17f553dcb425dac8\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-core:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\be04f8773d381722a4b4262f7c46e102\transformed\ui-core-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-core:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\be04f8773d381722a4b4262f7c46e102\transformed\ui-core-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c8ab8ff0816e2da218676ecd718a2fdd\transformed\imagepipeline-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c8ab8ff0816e2da218676ecd718a2fdd\transformed\imagepipeline-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\79f3259516df22757d258a93b0a50829\transformed\imagepipeline-base-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-base:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\79f3259516df22757d258a93b0a50829\transformed\imagepipeline-base-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\2.0.21\222b2be42672d47c002c1b22ac9f030d781fc5db\kotlin-stdlib-jdk7-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name=":@@:react-native-async-storage_async-storage::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5e97d2a22374df0226224d8bbecd6f93\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\5e97d2a22374df0226224d8bbecd6f93\transformed\out\jars\libs\R.jar"
      resolved="FoodyQueen Captain:react-native-async-storage_async-storage:unspecified"
      partialResultsDir="K:\2025\captainapp_native\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5e97d2a22374df0226224d8bbecd6f93\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-gesture-handler::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4e48fa64f0788ec77fc0968d93f20e83\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\4e48fa64f0788ec77fc0968d93f20e83\transformed\out\jars\libs\R.jar"
      resolved="FoodyQueen Captain:react-native-gesture-handler:unspecified"
      partialResultsDir="K:\2025\captainapp_native\node_modules\react-native-gesture-handler\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4e48fa64f0788ec77fc0968d93f20e83\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-reanimated::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\83fd837bc18aeba9aa9b841c1554018b\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\83fd837bc18aeba9aa9b841c1554018b\transformed\out\jars\libs\R.jar"
      resolved="FoodyQueen Captain:react-native-reanimated:3.17.5"
      partialResultsDir="K:\2025\captainapp_native\node_modules\react-native-reanimated\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\83fd837bc18aeba9aa9b841c1554018b\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-safe-area-context::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5eff3284dac1b94cb66f483d20c1043a\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\5eff3284dac1b94cb66f483d20c1043a\transformed\out\jars\libs\R.jar"
      resolved="FoodyQueen Captain:react-native-safe-area-context:unspecified"
      partialResultsDir="K:\2025\captainapp_native\node_modules\react-native-safe-area-context\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5eff3284dac1b94cb66f483d20c1043a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-webview::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\542bdc4cac4380316444eeb8d839d806\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\542bdc4cac4380316444eeb8d839d806\transformed\out\jars\libs\R.jar"
      resolved="FoodyQueen Captain:react-native-webview:unspecified"
      partialResultsDir="K:\2025\captainapp_native\node_modules\react-native-webview\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\542bdc4cac4380316444eeb8d839d806\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-gif:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2edb4108b6d3af54f55ac8f7a3fd9ae0\transformed\animated-gif-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-gif:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2edb4108b6d3af54f55ac8f7a3fd9ae0\transformed\animated-gif-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:webpsupport:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0297df8fcc2974bf1a6749148dbc26c0\transformed\webpsupport-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:webpsupport:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0297df8fcc2974bf1a6749148dbc26c0\transformed\webpsupport-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:hermes-android:0.79.4:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\89ae9be5931f26e4698dba75fe74eeda\transformed\hermes-android-0.79.4-release\jars\classes.jar"
      resolved="com.facebook.react:hermes-android:0.79.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\89ae9be5931f26e4698dba75fe74eeda\transformed\hermes-android-0.79.4-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\eb45ff10c0db144c5a5537429e93efe3\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\eb45ff10c0db144c5a5537429e93efe3\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6126635189ee6e9915c55ed159ffcefd\transformed\autofill-1.1.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6126635189ee6e9915c55ed159ffcefd\transformed\autofill-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fbjni:fbjni:0.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f7366e53586a12172ab374397325a4bf\transformed\fbjni-0.7.0\jars\classes.jar"
      resolved="com.facebook.fbjni:fbjni:0.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f7366e53586a12172ab374397325a4bf\transformed\fbjni-0.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:soloader:0.12.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fdb63001aee1d718e95ecb2b3adfa162\transformed\soloader-0.12.1\jars\classes.jar"
      resolved="com.facebook.soloader:soloader:0.12.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fdb63001aee1d718e95ecb2b3adfa162\transformed\soloader-0.12.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:nativeloader:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\nativeloader\0.12.1\492cc5082540e19b29328f2f56c53255cb6e7cc6\nativeloader-0.12.1.jar"
      resolved="com.facebook.soloader:nativeloader:0.12.1"/>
  <library
      name="com.facebook.soloader:annotation:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\annotation\0.12.1\945ada76f62253ba8e72cbf755d0e85ea7362cfe\annotation-0.12.1.jar"
      resolved="com.facebook.soloader:annotation:0.12.1"/>
  <library
      name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.infer.annotation\infer-annotation\0.18.0\27539793fe93ed7d92b6376281c16cda8278ab2f\infer-annotation-0.18.0.jar"
      resolved="com.facebook.infer.annotation:infer-annotation:0.18.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-annotations-jvm\1.3.72\7dba6c57de526588d8080317bda0c14cd88c8055\kotlin-annotations-jvm-1.3.72.jar"
      resolved="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72"/>
  <library
      name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3fdf4f45aa1f8b9633b95505ee88a374\transformed\imagepipeline-native-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-native:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3fdf4f45aa1f8b9633b95505ee88a374\transformed\imagepipeline-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a4e405a364064355f0d76d95d666e4c3\transformed\memory-type-ashmem-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-ashmem:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a4e405a364064355f0d76d95d666e4c3\transformed\memory-type-ashmem-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-native:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d59e45c858309aed0d8a0127b93222a\transformed\memory-type-native-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-native:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d59e45c858309aed0d8a0127b93222a\transformed\memory-type-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-java:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\35c756a193f6db29047132ee07537a49\transformed\memory-type-java-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-java:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\35c756a193f6db29047132ee07537a49\transformed\memory-type-java-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4ba88b7da306cdab7c7db5ea63a68613\transformed\nativeimagefilters-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagefilters:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4ba88b7da306cdab7c7db5ea63a68613\transformed\nativeimagefilters-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c582b1007c5176013781b139f8986a2c\transformed\nativeimagetranscoder-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagetranscoder:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c582b1007c5176013781b139f8986a2c\transformed\nativeimagetranscoder-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.yoga\proguard-annotations\1.19.0\fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63\proguard-annotations-1.19.0.jar"
      resolved="com.facebook.yoga:proguard-annotations:1.19.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="commons-io:commons-io:2.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.6\815893df5f31da2ece4040fe0a12fd44b577afaf\commons-io-2.6.jar"
      resolved="commons-io:commons-io:2.6"/>
  <library
      name="commons-codec:commons-codec:1.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.10\4b95f4897fa13f2cd904aee711aeafc0c5295cd8\commons-codec-1.10.jar"
      resolved="commons-codec:commons-codec:1.10"/>
  <library
      name="expo.modules.asset:expo.modules.asset:11.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e8a3e9b5e96af02342a176ccdc544ece\transformed\expo.modules.asset-11.1.5\jars\classes.jar"
      resolved="expo.modules.asset:expo.modules.asset:11.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e8a3e9b5e96af02342a176ccdc544ece\transformed\expo.modules.asset-11.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.blur:14.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6de1916b90041fd9e517ff96a11caa6d\transformed\expo.modules.blur-14.1.5\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.blur:14.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6de1916b90041fd9e517ff96a11caa6d\transformed\expo.modules.blur-14.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.font:13.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d97e291462cdacba71e8ea9143529a40\transformed\expo.modules.font-13.3.1\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.font:13.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d97e291462cdacba71e8ea9143529a40\transformed\expo.modules.font-13.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.16.0\411aa175d50d10b37c7a1a04d21a4e7145249557\disklrucache-4.16.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.16.0"/>
  <library
      name="com.github.bumptech.glide:annotations:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.16.0\90730f6498299d207aa0878124ab7585969808f0\annotations-4.16.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.16.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\436799b26b86e567b064386e20bb09aa\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\436799b26b86e567b064386e20bb09aa\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.caverock:androidsvg-aar:1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0ced2c618cc0851d3ee11456ab9ae13d\transformed\androidsvg-aar-1.4\jars\classes.jar"
      resolved="com.caverock:androidsvg-aar:1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0ced2c618cc0851d3ee11456ab9ae13d\transformed\androidsvg-aar-1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.keepawake:14.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9e4cfa109c9b9afbaf07c5dc128efb23\transformed\expo.modules.keepawake-14.1.4\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.keepawake:14.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9e4cfa109c9b9afbaf07c5dc128efb23\transformed\expo.modules.keepawake-14.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.screenorientation:8.1.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\af5b49f1e11ab757a57186805705ed9e\transformed\expo.modules.screenorientation-8.1.7\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.screenorientation:8.1.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\af5b49f1e11ab757a57186805705ed9e\transformed\expo.modules.screenorientation-8.1.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.splashscreen:0.30.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b3390bd7dd069ce47ea2215a9701dc04\transformed\expo.modules.splashscreen-0.30.9\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.splashscreen:0.30.9"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b3390bd7dd069ce47ea2215a9701dc04\transformed\expo.modules.splashscreen-0.30.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.systemui:5.0.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\107aba075f519d3df9f27b8031d582e4\transformed\expo.modules.systemui-5.0.8\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.systemui:5.0.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\107aba075f519d3df9f27b8031d582e4\transformed\expo.modules.systemui-5.0.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-linking::release"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1cedd943553fdcbc0349c6c81cf898ee\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\1cedd943553fdcbc0349c6c81cf898ee\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-linking:7.1.5"
      partialResultsDir="K:\2025\captainapp_native\node_modules\expo-linking\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1cedd943553fdcbc0349c6c81cf898ee\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1688e4f4998421f6cdbc59f17cef2f61\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1688e4f4998421f6cdbc59f17cef2f61\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-splashscreen:1.2.0-alpha02@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e03d0108970ad49120068551e56f0044\transformed\core-splashscreen-1.2.0-alpha02\jars\classes.jar"
      resolved="androidx.core:core-splashscreen:1.2.0-alpha02"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e03d0108970ad49120068551e56f0044\transformed\core-splashscreen-1.2.0-alpha02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.penfeizhou.android.animation:glide-plugin:3.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f40077589a7ac8623ba297258e75d28b\transformed\glide-plugin-3.0.3\jars\classes.jar"
      resolved="com.github.penfeizhou.android.animation:glide-plugin:3.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f40077589a7ac8623ba297258e75d28b\transformed\glide-plugin-3.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4a6ff7a886435c160fc114598df514e8\transformed\constraintlayout-2.0.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4a6ff7a886435c160fc114598df514e8\transformed\constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.penfeizhou.android.animation:awebp:3.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ee6ee78b8b7385b6567f6e3572e56c2\transformed\awebp-3.0.3\jars\classes.jar"
      resolved="com.github.penfeizhou.android.animation:awebp:3.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ee6ee78b8b7385b6567f6e3572e56c2\transformed\awebp-3.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.penfeizhou.android.animation:apng:3.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e02d6619476aba0105310be2159d96f9\transformed\apng-3.0.3\jars\classes.jar"
      resolved="com.github.penfeizhou.android.animation:apng:3.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e02d6619476aba0105310be2159d96f9\transformed\apng-3.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.penfeizhou.android.animation:gif:3.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ea85dbefea5c1958d4cdd80a099d89cf\transformed\gif-3.0.3\jars\classes.jar"
      resolved="com.github.penfeizhou.android.animation:gif:3.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ea85dbefea5c1958d4cdd80a099d89cf\transformed\gif-3.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.penfeizhou.android.animation:avif:3.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fa93cb06fb117b034e9e00e2254e875d\transformed\avif-3.0.3\jars\classes.jar"
      resolved="com.github.penfeizhou.android.animation:avif:3.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fa93cb06fb117b034e9e00e2254e875d\transformed\avif-3.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.penfeizhou.android.animation:frameanimation:3.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\76f79e0c31791936fb2bd92707f6357d\transformed\frameanimation-3.0.3\jars\classes.jar"
      resolved="com.github.penfeizhou.android.animation:frameanimation:3.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\76f79e0c31791936fb2bd92707f6357d\transformed\frameanimation-3.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\45a5fcb4b05df7123a46aa86205607f0\transformed\fragment-ktx-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\45a5fcb4b05df7123a46aa86205607f0\transformed\fragment-ktx-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:avif-integration:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\65acae60dc05898669088afb756e6a62\transformed\avif-integration-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:avif-integration:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\65acae60dc05898669088afb756e6a62\transformed\avif-integration-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="jp.wasabeef:glide-transformations:4.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9172c3d7ced60217e2cab8dc4ac29a\transformed\glide-transformations-4.3.0\jars\classes.jar"
      resolved="jp.wasabeef:glide-transformations:4.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9172c3d7ced60217e2cab8dc4ac29a\transformed\glide-transformations-4.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0a985a726a0ed91053e59647f893676a\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0a985a726a0ed91053e59647f893676a\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e03b29ff010dcda13836cde37647491\transformed\activity-ktx-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e03b29ff010dcda13836cde37647491\transformed\activity-ktx-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\86cbd6534acf09b6edb34f1ee4cf1ea6\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\86cbd6534acf09b6edb34f1ee4cf1ea6\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c7cd1ae60e32cf15d1d605bbf04384dc\transformed\webkit-1.4.0\jars\classes.jar"
      resolved="androidx.webkit:webkit:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c7cd1ae60e32cf15d1d605bbf04384dc\transformed\webkit-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-base:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6c0c3d9644f9203b6dc5a2f3ebd3236b\transformed\animated-base-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-base:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6c0c3d9644f9203b6dc5a2f3ebd3236b\transformed\animated-base-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-drawable:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1f4c51f0bcade3115e42ccfe51bd59a1\transformed\animated-drawable-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-drawable:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1f4c51f0bcade3115e42ccfe51bd59a1\transformed\animated-drawable-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-options:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ba9ef01bf112dd567fa66445cb5c61e2\transformed\vito-options-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-options:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ba9ef01bf112dd567fa66445cb5c61e2\transformed\vito-options-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:urimod:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\cde481b0d533be33fa0f770b40e08f80\transformed\urimod-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:urimod:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\cde481b0d533be33fa0f770b40e08f80\transformed\urimod-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-source:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\da0dd16585cfbcbe69014c3bea92fdb6\transformed\vito-source-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-source:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\da0dd16585cfbcbe69014c3bea92fdb6\transformed\vito-source-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:soloader:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d2b07f954987ada6e44a53492ccc0edd\transformed\soloader-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:soloader:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d2b07f954987ada6e44a53492ccc0edd\transformed\soloader-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bdbe8332f585138dd54b686bb64a82aa\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bdbe8332f585138dd54b686bb64a82aa\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ce19d4bbc6c73debd3675c7709924648\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ce19d4bbc6c73debd3675c7709924648\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8109800e0431b448d21295476395ef1d\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8109800e0431b448d21295476395ef1d\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bdcbec29e8d0b717843f3dbae0b94f84\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\bdcbec29e8d0b717843f3dbae0b94f84\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bdcbec29e8d0b717843f3dbae0b94f84\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\47a56a4f1be0e9a7d815855bffa6881d\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\47a56a4f1be0e9a7d815855bffa6881d\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6a7fec56ee66dd7f7a0a2b21f81fa72c\transformed\lifecycle-viewmodel-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6a7fec56ee66dd7f7a0a2b21f81fa72c\transformed\lifecycle-viewmodel-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fece13e9c08c13338369de69cd14e88c\transformed\lifecycle-runtime-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fece13e9c08c13338369de69cd14e88c\transformed\lifecycle-runtime-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3f62d4745054bc29342c5607bcde4d\transformed\lifecycle-process-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3f62d4745054bc29342c5607bcde4d\transformed\lifecycle-process-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\10b213ecea0e4e52da532b6ba0f83370\transformed\lifecycle-livedata-core-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\10b213ecea0e4e52da532b6ba0f83370\transformed\lifecycle-livedata-core-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-reflect:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\2.0.21\669e1d35e4ca1797f9ddb2830dd6c36c0ca531e4\kotlin-reflect-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-reflect:2.0.21"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3070f604e3c80036d56e6e100694a483\transformed\profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b02b404f32dd5de34e82e545d50d8192\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b02b404f32dd5de34e82e545d50d8192\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1bbf8401d34e5d62926e60ab182c1f96\transformed\tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1bbf8401d34e5d62926e60ab182c1f96\transformed\tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="com.facebook.fresco:vito-renderer:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\52f3a2fa8c0df5a8414fb52db1dd2780\transformed\vito-renderer-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-renderer:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\52f3a2fa8c0df5a8414fb52db1dd2780\transformed\vito-renderer-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.Dimezis:BlurView:version-2.0.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\67a76227458514088161a2b0747e4617\transformed\BlurView-version-2.0.6\jars\classes.jar"
      resolved="com.github.Dimezis:BlurView:version-2.0.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\67a76227458514088161a2b0747e4617\transformed\BlurView-version-2.0.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\57b0ed4888c5d35f21d94e7e07d01862\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\57b0ed4888c5d35f21d94e7e07d01862\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.guava:guava:28.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\28.1-android\c2526f8fad32a65a6d7032dd8e9524eb276b108b\guava-28.1-android.jar"
      resolved="com.google.guava:guava:28.1-android"/>
  <library
      name="com.parse.bolts:bolts-tasks:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.parse.bolts\bolts-tasks\1.4.0\d85884acf6810a3bbbecb587f239005cbc846dc4\bolts-tasks-1.4.0.jar"
      resolved="com.parse.bolts:bolts-tasks:1.4.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.15.0\38c8485a652f808c8c149150da4e5c2b0bd17f9a\error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="org.aomedia.avif.android:avif:1.0.1.262e11d@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fd97a93c331c36d4de146222ebe73ebb\transformed\avif-1.0.1.262e11d\jars\classes.jar"
      resolved="org.aomedia.avif.android:avif:1.0.1.262e11d"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fd97a93c331c36d4de146222ebe73ebb\transformed\avif-1.0.1.262e11d"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-solver\2.0.1\30988fe2d77f3fe3bf7551bb8a8b795fad7e7226\constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="org.checkerframework:checker-compat-qual:2.5.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-compat-qual\2.5.5\435dc33e3019c9f019e15f01aa111de9d6b2b79c\checker-compat-qual-2.5.5.jar"
      resolved="org.checkerframework:checker-compat-qual:2.5.5"/>
  <library
      name="com.google.j2objc:j2objc-annotations:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\1.3\ba035118bc8bac37d7eff77700720999acd9986d\j2objc-annotations-1.3.jar"
      resolved="com.google.j2objc:j2objc-annotations:1.3"/>
  <library
      name="org.codehaus.mojo:animal-sniffer-annotations:1.18@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.codehaus.mojo\animal-sniffer-annotations\1.18\f7aa683ea79dc6681ee9fb95756c999acbb62f5d\animal-sniffer-annotations-1.18.jar"
      resolved="org.codehaus.mojo:animal-sniffer-annotations:1.18"/>
</libraries>
