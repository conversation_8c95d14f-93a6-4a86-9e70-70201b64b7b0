<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\android\app\src\main\res"><file name="ic_launcher_background" path="K:\2025\captainapp_native\android\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="rn_edit_text_material" path="K:\2025\captainapp_native\android\app\src\main\res\drawable\rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="splashscreen_logo" path="K:\2025\captainapp_native\android\app\src\main\res\drawable-hdpi\splashscreen_logo.png" qualifiers="hdpi-v4" type="drawable"/><file name="splashscreen_logo" path="K:\2025\captainapp_native\android\app\src\main\res\drawable-mdpi\splashscreen_logo.png" qualifiers="mdpi-v4" type="drawable"/><file name="splashscreen_logo" path="K:\2025\captainapp_native\android\app\src\main\res\drawable-xhdpi\splashscreen_logo.png" qualifiers="xhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="K:\2025\captainapp_native\android\app\src\main\res\drawable-xxhdpi\splashscreen_logo.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="K:\2025\captainapp_native\android\app\src\main\res\drawable-xxxhdpi\splashscreen_logo.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="K:\2025\captainapp_native\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="K:\2025\captainapp_native\android\app\src\main\res\values\colors.xml" qualifiers=""><color name="splashscreen_background">#ffffff</color><color name="iconBackground">#000000</color><color name="colorPrimary">#023c69</color><color name="colorPrimaryDark">#ffffff</color></file><file path="K:\2025\captainapp_native\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">FoodyQueen Captain</string><string name="expo_system_ui_user_interface_style" translatable="false">automatic</string><string name="expo_splash_screen_resize_mode" translatable="false">contain</string><string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string></file><file path="K:\2025\captainapp_native\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.EdgeToEdge">
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
  </style><style name="Theme.App.SplashScreen" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/splashscreen_background</item>
    <item name="windowSplashScreenAnimatedIcon">@drawable/splashscreen_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style></file><file path="K:\2025\captainapp_native\android\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"/><file name="network_security_config" path="K:\2025\captainapp_native\android\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\android\app\build\generated\res\resValues\release"/><source path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="K:\2025\captainapp_native\android\app\build\generated\res\resValues\release"><file path="K:\2025\captainapp_native\android\app\build\generated\res\resValues\release\values\gradleResValues.xml" qualifiers=""><integer name="react_native_dev_server_port">8081</integer></file></source><source path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets"><file name="assets_images_captain" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\assets_images_captain.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_exporouter_assets_arrow_down" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_exporouter_assets_arrow_down.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_exporouter_assets_error" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_exporouter_assets_error.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_exporouter_assets_file" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_exporouter_assets_file.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_exporouter_assets_forward" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_exporouter_assets_forward.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_exporouter_assets_pkg" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_exporouter_assets_pkg.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_exporouter_assets_sitemap" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_exporouter_assets_sitemap.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_exporouter_assets_unmatched" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_exporouter_assets_unmatched.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backiconmask" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_backiconmask.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="keep" path="K:\2025\captainapp_native\android\app\build\generated\res\createBundleReleaseJsAndAssets\raw\keep.xml" qualifiers="" type="raw"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-createBundleReleaseJsAndAssets$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-createBundleReleaseJsAndAssets" generated-set="res-createBundleReleaseJsAndAssets$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>