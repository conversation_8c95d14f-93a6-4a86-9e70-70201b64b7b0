import AsyncStorage from "@react-native-async-storage/async-storage";
import { Image } from "expo-image";
import { useKeepAwake } from "expo-keep-awake";
import { useRouter } from "expo-router";
import { AnimatePresence, MotiView } from "moti";
import { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  BackHandler,
  Platform,
  StatusBar,
  StyleSheet,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { WebView } from "react-native-webview";

export default function WebViewComponent() {
  const [url, setUrl] = useState(null);
  const [statusBarColor, setStatusBarColor] = useState("#ffffff");
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const webViewRef = useRef(null);

  // Keep the app awake while this component is mounted
  useKeepAwake();

  useEffect(() => {
    const loadStoredUrl = async () => {
      try {
        const storedIp = await AsyncStorage.getItem("fullip");
        if (storedIp) {
          // Construct the KDS URL with port 8500 and route /kds.html
          // const kdsUrl = `${storedIp}/kds`;
          const kdsUrl = `${storedIp}/captainapp.html`;
          setUrl(kdsUrl);
        } else {
          // Redirect to sync code screen if no IP is stored
          router.replace("/synccode");
        }
      } catch (error) {
        console.error("Error loading stored URL:", error);
        router.replace("/synccode");
      }
    };

    loadStoredUrl();
  }, [router]);
  // useEffect(() => {
  //   // Lock to landscape on mount
  //   ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE);

  //   // Optional: Reset to default on unmount
  //   return () => {
  //     ScreenOrientation.unlockAsync(); // or use PORTRAIT if needed
  //   };
  // }, []);

  useEffect(() => {
    const backAction = () => {
      if (webViewRef.current) {
        webViewRef.current.injectJavaScript(`
          if (window.handleBackButton) {
            window.handleBackButton();
          }
          true;
        `);
      }
      return true; // prevent native back
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );
    return () => backHandler.remove();
  }, []);

  const INJECTED_CSS = `
    html, body {
      height: 100%;
      width: 100%;
      margin: 0;
      padding: 0;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      background-color: #ffffff;
    }
    body {
      display: flex;
      flex-direction: column;
    }
    * {
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
    }
    .content-wrapper {
      min-height: 100vh;
    }
  `;

  const INJECTED_JAVASCRIPT = `
    (function() {
      // Prevent pull-to-refresh behavior
      document.body.style.overscrollBehavior = 'none';

      // Adjust viewport for mobile
      var meta = document.createElement('meta');
      meta.name = 'viewport';
      meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
      document.getElementsByTagName('head')[0].appendChild(meta);

      // Disable text selection for app-like feel
      document.documentElement.style.webkitUserSelect = 'none';
      document.documentElement.style.userSelect = 'none';

      // Prevent long press context menu
      document.documentElement.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
      });

      // Wrap body content in a div with content-wrapper class to prevent stretching
      if (!document.querySelector('.content-wrapper')) {
        const wrapper = document.createElement('div');
        wrapper.className = 'content-wrapper';

        // Move all body children into the wrapper
        while (document.body.firstChild) {
          wrapper.appendChild(document.body.firstChild);
        }

        // Append the wrapper to the body
        document.body.appendChild(wrapper);
      }

      true; // Note: this is needed for the injected script to work
    })();
  `;

  const onWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === "CHANGE_STATUSBAR" && data.color) {
        setStatusBarColor(data.color);
      }
    } catch (e) {
      console.warn("Invalid message from WebView:", e);
    }
  };

  const handleError = (error) => {
    console.error("WebView error:", error);
    router.replace("/synccode");
  };

  if (!url) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#296eeb" />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {Platform.OS === "android" && (
        <StatusBar backgroundColor={statusBarColor} barStyle="dark-content" />
      )}

      <WebView
        ref={webViewRef}
        source={{ uri: url }}
        originWhitelist={["*"]}
        allowsLocalNetworkAccess={true}
        onLoadStart={() => setIsLoading(true)}
        onError={handleError}
        onHttpError={handleError}
        onLoadEnd={() => setIsLoading(false)}
        allowsInlineMediaPlayback={true}
        allowFileAccess={true}
        mediaPlaybackRequiresUserAction={false}
        allowUniversalAccessFromFileURLs={true}
        allowFileAccessFromFileURLs={true}
        domStorageEnabled={true}
        javaScriptEnabled={true}
        mixedContentMode="always"
        overScrollMode="never"
        bounces={false}
        scrollEnabled={true}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        containerStyle={styles.webViewContainer}
        androidHardwareAccelerationDisabled={false}
        androidLayerType="hardware"
        cacheEnabled={true}
        cacheMode="LOAD_DEFAULT"
        decelerationRate={0.998}
        startInLoadingState={true}
        directionalLockEnabled={Platform.OS === "ios"}
        hideKeyboardAccessoryView={Platform.OS === "ios"}
        keyboardDisplayRequiresUserAction={false}
        scalesPageToFit={Platform.OS === "ios" ? false : undefined}
        useWebKit={Platform.OS === "ios"}
        renderLoading={() => (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#296eeb" />
          </View>
        )}
        userAgent={Platform.select({
          android:
            "Mozilla/5.0 (Linux; Android 10; Android SDK built for x86) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36 CaptainApp/1.0",
          ios: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1 CaptainApp/1.0",
        })}
        injectedJavaScript={INJECTED_JAVASCRIPT}
        injectedJavaScriptBeforeContentLoaded={`
          const style = document.createElement('style');
          style.textContent = \`${INJECTED_CSS}\`;
          document.head.append(style);
        `}
        onMessage={onWebViewMessage}
      />

      <AnimatePresence>
        {isLoading && (
          <MotiView
            from={{ opacity: 1 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={[
              StyleSheet.absoluteFillObject,
              {
                backgroundColor: "white",
                zIndex: 10,
                justifyContent: "center",
                alignItems: "center",
              },
            ]}
          >
            <Image
              // source={require("../assets/images/icon.png")}
              source={require("../assets/images/captain.png")}
              style={styles.logo}
            />
            <ActivityIndicator size="small" color="#296eeb" />
          </MotiView>
        )}
      </AnimatePresence>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    overflow: "hidden",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#fff",
  },
  webViewContainer: {
    flex: 1,
    height: "100%",
    width: "100%",
    overflow: "hidden",
    borderWidth: 0,
    borderRadius: 0,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: 20,
  },
});
