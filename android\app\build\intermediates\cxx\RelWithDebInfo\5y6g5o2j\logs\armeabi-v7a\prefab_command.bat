@echo off
"C:\\Program Files\\OpenJDK\\jdk-22.0.2\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  armeabi-v7a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging12130540204274667439\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55e5e2b47fc8f51b895ce8757651a2de\\transformed\\react-android-0.79.4-release\\prefab" ^
  "K:\\2025\\captainapp_native\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\6b2w2b47" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\89ae9be5931f26e4698dba75fe74eeda\\transformed\\hermes-android-0.79.4-release\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f7366e53586a12172ab374397325a4bf\\transformed\\fbjni-0.7.0\\prefab"
