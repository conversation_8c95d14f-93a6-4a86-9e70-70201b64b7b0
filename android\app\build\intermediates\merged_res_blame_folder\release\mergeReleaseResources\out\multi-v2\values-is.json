{"logs": [{"outputFile": "com.kingsanu.captainapp-mergeReleaseResources-56:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\25ad4bdeddd79d498a0e9dfe4af5a7d2\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "38,39,40,41,42,43,44,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3381,3476,3583,3680,3780,3883,3987,10114", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3471,3578,3675,3775,3878,3982,4093,10210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1cc5e9e2cf9bf5411b10463ffe5bdf1d\\transformed\\appcompat-1.7.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,420,517,629,714,815,929,1010,1089,1180,1273,1366,1460,1566,1659,1754,1849,1940,2034,2115,2225,2332,2429,2538,2638,2741,2896,9795", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "415,512,624,709,810,924,1005,1084,1175,1268,1361,1455,1561,1654,1749,1844,1935,2029,2110,2220,2327,2424,2533,2633,2736,2891,2989,9871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1688e4f4998421f6cdbc59f17cef2f61\\transformed\\material-1.12.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1009,1074,1162,1226,1287,1377,1441,1504,1566,1634,1698,1754,1877,1942,2004,2060,2131,2258,2342,2416,2513,2594,2678,2814,2891,2968,3084,3171,3250,3307,3362,3428,3504,3584,3655,3731,3798,3872,3942,4008,4110,4196,4266,4357,4447,4521,4594,4683,4734,4815,4887,4968,5054,5116,5180,5243,5312,5426,5532,5640,5742,5803,5862,5942,6026,6105", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79,83,78,74", "endOffsets": "265,339,411,490,572,652,749,864,946,1004,1069,1157,1221,1282,1372,1436,1499,1561,1629,1693,1749,1872,1937,1999,2055,2126,2253,2337,2411,2508,2589,2673,2809,2886,2963,3079,3166,3245,3302,3357,3423,3499,3579,3650,3726,3793,3867,3937,4003,4105,4191,4261,4352,4442,4516,4589,4678,4729,4810,4882,4963,5049,5111,5175,5238,5307,5421,5527,5635,5737,5798,5857,5937,6021,6100,6175"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,51,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2994,3068,3140,3219,3301,4098,4195,4310,4496,4554,4619,5015,5079,5140,5230,5294,5357,5419,5487,5551,5607,5730,5795,5857,5913,5984,6111,6195,6269,6366,6447,6531,6667,6744,6821,6937,7024,7103,7160,7215,7281,7357,7437,7508,7584,7651,7725,7795,7861,7963,8049,8119,8210,8300,8374,8447,8536,8587,8668,8740,8821,8907,8969,9033,9096,9165,9279,9385,9493,9595,9656,9715,9876,9960,10039", "endLines": "5,33,34,35,36,37,45,46,47,49,50,51,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,117,118,119", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79,83,78,74", "endOffsets": "315,3063,3135,3214,3296,3376,4190,4305,4387,4549,4614,4702,5074,5135,5225,5289,5352,5414,5482,5546,5602,5725,5790,5852,5908,5979,6106,6190,6264,6361,6442,6526,6662,6739,6816,6932,7019,7098,7155,7210,7276,7352,7432,7503,7579,7646,7720,7790,7856,7958,8044,8114,8205,8295,8369,8442,8531,8582,8663,8735,8816,8902,8964,9028,9091,9160,9274,9380,9488,9590,9651,9710,9790,9955,10034,10109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\44d5193287b18dec49d9cd0bdd73b6e9\\transformed\\browser-1.6.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "48,52,53,54", "startColumns": "4,4,4,4", "startOffsets": "4392,4707,4808,4914", "endColumns": "103,100,105,100", "endOffsets": "4491,4803,4909,5010"}}]}]}