R_DEF: Internal format may change without notice
local
color colorPrimary
color colorPrimaryDark
color iconBackground
color splashscreen_background
drawable assets_images_captain
drawable ic_launcher_background
drawable node_modules_exporouter_assets_arrow_down
drawable node_modules_exporouter_assets_error
drawable node_modules_exporouter_assets_file
drawable node_modules_exporouter_assets_forward
drawable node_modules_exporouter_assets_pkg
drawable node_modules_exporouter_assets_sitemap
drawable node_modules_exporouter_assets_unmatched
drawable node_modules_reactnavigation_elements_lib_module_assets_backicon
drawable node_modules_reactnavigation_elements_lib_module_assets_backiconmask
drawable node_modules_reactnavigation_elements_lib_module_assets_clearicon
drawable node_modules_reactnavigation_elements_lib_module_assets_closeicon
drawable node_modules_reactnavigation_elements_lib_module_assets_searchicon
drawable rn_edit_text_material
drawable splashscreen_logo
integer react_native_dev_server_port
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_round
raw keep
string app_name
string expo_splash_screen_resize_mode
string expo_splash_screen_status_bar_translucent
string expo_system_ui_user_interface_style
style AppTheme
style Theme.App.SplashScreen
xml network_security_config
