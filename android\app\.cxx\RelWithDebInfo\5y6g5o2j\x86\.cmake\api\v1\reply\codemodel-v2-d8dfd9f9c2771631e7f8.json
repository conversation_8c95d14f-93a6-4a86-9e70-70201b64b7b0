{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-RelWithDebInfo-ffb90199b8dc646e6514.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/captainapp_native/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-RelWithDebInfo-05ec32cef99874260fc7.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/captainapp_native/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-RelWithDebInfo-e72d37b30fdfd681bddb.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/captainapp_native/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-RelWithDebInfo-144b81b36b549c0795e9.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/captainapp_native/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [7]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-RelWithDebInfo-0c4fc5449a915f984811.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/captainapp_native/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [6]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-RelWithDebInfo-f9d478634c0b298f5ce2.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/captainapp_native/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "RNEdgeToEdge_autolinked_build", "jsonFile": "directory-RNEdgeToEdge_autolinked_build-RelWithDebInfo-b04b7df7d10e38e26e2f.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/captainapp_native/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni", "targetIndexes": [2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-RelWithDebInfo-25ae74e58c3761eb9f1d.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-RelWithDebInfo-d47ff51a2d3e67ff16ba.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e", "jsonFile": "target-react_codegen_RNEdgeToEdge-RelWithDebInfo-54ea179ccd0adb2547ad.json", "name": "react_codegen_RNEdgeToEdge", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-RelWithDebInfo-9cf4eab53ef11a99a738.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-RelWithDebInfo-f86f1ab4c42756733b5a.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-RelWithDebInfo-fd18c33f95645e1d35d1.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-RelWithDebInfo-e5d161182ffddb24a711.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-RelWithDebInfo-fd612dccc181beb4f43a.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "K:/2025/captainapp_native/android/app/.cxx/RelWithDebInfo/5y6g5o2j/x86", "source": "K:/2025/captainapp_native/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}