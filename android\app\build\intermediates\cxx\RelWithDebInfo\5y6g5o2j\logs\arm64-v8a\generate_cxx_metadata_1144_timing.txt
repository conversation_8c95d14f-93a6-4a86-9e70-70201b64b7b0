# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    [gap of 84ms]
    exec-prefab 1021ms
    [gap of 55ms]
  generate-prefab-packages completed in 1160ms
  execute-generate-process
    exec-configure 8979ms
    [gap of 951ms]
  execute-generate-process completed in 9934ms
  [gap of 36ms]
  remove-unexpected-so-files 13ms
  [gap of 44ms]
generate_cxx_metadata completed in 11202ms

