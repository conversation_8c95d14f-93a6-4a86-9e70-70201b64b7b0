[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "K:\\2025\\captainapp_native\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'K:\\2025\\captainapp_native\\android\\app\\.cxx\\RelWithDebInfo\\5y6g5o2j\\x86_64\\android_gradle_build.json' was up-to-date", "file_": "K:\\2025\\captainapp_native\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "K:\\2025\\captainapp_native\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]