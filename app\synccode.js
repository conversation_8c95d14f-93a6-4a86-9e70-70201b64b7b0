import AsyncStorage from "@react-native-async-storage/async-storage";
import axios from "axios";
import { Camera, CameraView } from "expo-camera";
import { useRouter } from "expo-router";
import { useEffect, useState } from "react";
import {
  Alert,
  Dimensions,
  Modal,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const { width, height } = Dimensions.get('window');

export default function SyncCodeScreen() {
  const [ip, setIp] = useState("");
  const [showScanner, setShowScanner] = useState(true); // Start with scanner active
  const [hasPermission, setHasPermission] = useState(null);
  const [showManualInput, setShowManualInput] = useState(false);
  const [connectionError, setConnectionError] = useState("");
  const router = useRouter();

  useEffect(() => {
    const getIt = async () => {
      let result = await AsyncStorage.getItem("ip");
      setIp(result || ""); // Handle null case
    };
    getIt();

    // Request camera permission
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  const extractIpFromQrCode = (qrData) => {
    try {
      // Try to parse as URL first
      const url = new URL(qrData);
      const params = new URLSearchParams(url.search);

      // Look for IP in various parameter names
      const ipParam = params.get('ip') || params.get('server') || params.get('host');
      if (ipParam) {
        return ipParam;
      }

      // If no URL parameters, check if the QR code contains an IP directly
      const ipRegex = /\b(?:\d{1,3}\.){3}\d{1,3}\b/;
      const ipMatch = qrData.match(ipRegex);
      if (ipMatch) {
        return ipMatch[0];
      }

      // Check for partial IP (last two octets)
      const partialIpRegex = /\b\d{1,3}\.\d{1,3}\b/;
      const partialMatch = qrData.match(partialIpRegex);
      if (partialMatch) {
        return partialMatch[0];
      }

      return null;
    } catch (error) {
      // If not a valid URL, try to extract IP directly
      const ipRegex = /\b(?:\d{1,3}\.){3}\d{1,3}\b/;
      const ipMatch = qrData.match(ipRegex);
      if (ipMatch) {
        return ipMatch[0];
      }

      // Check for partial IP (last two octets)
      const partialIpRegex = /\b\d{1,3}\.\d{1,3}\b/;
      const partialMatch = qrData.match(partialIpRegex);
      if (partialMatch) {
        return partialMatch[0];
      }

      return null;
    }
  };

  const handleQrCodeScanned = ({ data }) => {
    const extractedIp = extractIpFromQrCode(data);
    if (extractedIp) {
      setIp(extractedIp);
      setShowScanner(false);
      setConnectionError("");
      // Automatically attempt connection
      connectWithIp(extractedIp);
    } else {
      setConnectionError("No valid IP address found in QR code");
    }
  };

  const connectWithIp = async (ipToConnect) => {
    const ipToUse = ipToConnect || ip;
    if (!ipToUse.trim()) {
      Alert.alert("Error", "Please enter a sync code");
      return;
    }

    try {
      let finalIp = ipToUse.trim();

      // Handle different IP input formats
      if (finalIp.includes(".")) {
        // If it contains dots, assume it's a complete IP
        if (!finalIp.startsWith("192.168.")) {
          // If it doesn't start with 192.168., add it
          finalIp = `192.168.${finalIp}`;
        }
      } else {
        // If no dots, assume it's just the last two octets
        finalIp = `192.168.${finalIp}`;
      }

      // Test connection to the server
      const testUrl = `http://${finalIp}:8500/pos/ping`;
      const res = await axios.get(testUrl, {
        timeout: 3000,
      });

      if (res.status === 200) {
        // Store the IP parts and full URL
        await AsyncStorage.setItem("ip", ipToUse.trim());
        await AsyncStorage.setItem("fullip", `http://${finalIp}:8500`);

        console.log("Connected to:", `http://${finalIp}:8500`);
        setConnectionError("");
        router.replace("/home");
      }
    } catch (err) {
      console.log("Error details:", err);
      const errorMessage = err.message || "Invalid IP or server not reachable";
      setConnectionError(errorMessage);
      Alert.alert("Connection Failed", errorMessage);
    }
  };

  const connect = async () => {
    await connectWithIp();
  };
  if (hasPermission === null) {
    return (
      <View style={styles.container}>
        <Text>Requesting camera permission...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>No access to camera</Text>
        <TouchableOpacity style={styles.manualButton} onPress={() => setShowManualInput(true)}>
          <Text style={styles.manualButtonText}>Link Manually</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.logo}>🍔 Foodyqueen</Text>
        <Text style={styles.subtitle}>Scan QR from foodyqueen POS</Text>
      </View>

      {/* QR Scanner Area */}
      <View style={styles.scannerContainer}>
        {showScanner ? (
          <CameraView
            style={styles.camera}
            onBarcodeScanned={handleQrCodeScanned}
            barcodeScannerSettings={{
              barcodeTypes: ['qr'],
            }}
          />
        ) : (
          <TouchableOpacity
            style={styles.scannerPlaceholder}
            onPress={() => setShowScanner(true)}
          >
            <View style={styles.scannerFrame}>
              <Text style={styles.scannerText}>Tap to Start QR Scanner</Text>
            </View>
          </TouchableOpacity>
        )}
      </View>

      {/* Manual Link Button */}
      <TouchableOpacity
        style={styles.manualButton}
        onPress={() => setShowManualInput(true)}
      >
        <Text style={styles.manualButtonText}>🔗 Link Manually</Text>
      </TouchableOpacity>

      {/* Error Message */}
      {connectionError ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Failed to connect with PC</Text>
          <Text style={styles.errorMessage}>Please scan QR again to link with foodyqueen</Text>
        </View>
      ) : null}



      {/* Manual Input Modal */}
      <Modal
        visible={showManualInput}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowManualInput(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Manual Connection</Text>
            <Text style={styles.modalSubtitle}>
              Enter IP address or last two octets (e.g., 1.100 or *************)
            </Text>
            <TextInput
              value={ip}
              keyboardType="numeric"
              onChangeText={setIp}
              placeholder="e.g., 1.100 or *************"
              placeholderTextColor="#888"
              style={styles.input}
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowManualInput(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.connectButton}
                onPress={() => {
                  setShowManualInput(false);
                  connect();
                }}
              >
                <Text style={styles.connectButtonText}>Connect</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2C2C2C',
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 30,
  },
  logo: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#CCCCCC',
  },
  scannerContainer: {
    flex: 1,
    marginHorizontal: 40,
    marginVertical: 20,
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: '#FFFFFF',
  },
  camera: {
    flex: 1,
  },
  scannerPlaceholder: {
    flex: 1,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerFrame: {
    width: 200,
    height: 200,
    borderWidth: 2,
    borderColor: '#CCCCCC',
    borderRadius: 10,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '500',
  },
  manualButton: {
    backgroundColor: '#4A4A4A',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 25,
    marginHorizontal: 40,
    marginBottom: 20,
    alignItems: 'center',
  },
  manualButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },

  errorContainer: {
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingBottom: 20,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 14,
    color: '#CCCCCC',
    textAlign: 'center',
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 30,
    marginHorizontal: 40,
    width: width - 80,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 10,
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },
  input: {
    borderWidth: 1,
    borderColor: '#DDDDDD',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F0F0F0',
    paddingVertical: 12,
    borderRadius: 10,
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '500',
  },
  connectButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    borderRadius: 10,
    marginLeft: 10,
    alignItems: 'center',
  },
  connectButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
