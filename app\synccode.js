import AsyncStorage from "@react-native-async-storage/async-storage";
import axios from "axios";
import { Camera, CameraView } from "expo-camera";
import { useRouter } from "expo-router";
import { useEffect, useState } from "react";
import {
  Alert,
  Dimensions,
  Image,
  Modal,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const { width, height } = Dimensions.get('window');

export default function SyncCodeScreen() {
  const [ip, setIp] = useState("");
  const [showScanner, setShowScanner] = useState(true); // Start with scanner active
  const [hasPermission, setHasPermission] = useState(null);
  const [showManualInput, setShowManualInput] = useState(false);
  const [isScanning, setIsScanning] = useState(true); // Control scanning state
  const router = useRouter();

  useEffect(() => {
    const getIt = async () => {
      let result = await AsyncStorage.getItem("ip");
      setIp(result || ""); // Handle null case
    };
    getIt();

    // Request camera permission
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  const extractIpFromQrCode = (qrData) => {
    try {
      // Try to parse as URL first
      const url = new URL(qrData);
      const params = new URLSearchParams(url.search);

      // Look for IP in various parameter names
      const ipParam = params.get('ip') || params.get('server') || params.get('host');
      if (ipParam) {
        return ipParam;
      }

      // If no URL parameters, check if the QR code contains an IP directly
      const ipRegex = /\b(?:\d{1,3}\.){3}\d{1,3}\b/;
      const ipMatch = qrData.match(ipRegex);
      if (ipMatch) {
        return ipMatch[0];
      }

      // Check for partial IP (last two octets)
      const partialIpRegex = /\b\d{1,3}\.\d{1,3}\b/;
      const partialMatch = qrData.match(partialIpRegex);
      if (partialMatch) {
        return partialMatch[0];
      }

      return null;
    } catch (error) {
      // If not a valid URL, try to extract IP directly
      const ipRegex = /\b(?:\d{1,3}\.){3}\d{1,3}\b/;
      const ipMatch = qrData.match(ipRegex);
      if (ipMatch) {
        return ipMatch[0];
      }

      // Check for partial IP (last two octets)
      const partialIpRegex = /\b\d{1,3}\.\d{1,3}\b/;
      const partialMatch = qrData.match(partialIpRegex);
      if (partialMatch) {
        return partialMatch[0];
      }

      return null;
    }
  };

  const handleQrCodeScanned = ({ data }) => {
    if (!isScanning) return; // Prevent scanning when disabled

    const extractedIp = extractIpFromQrCode(data);
    if (extractedIp) {
      setIp(extractedIp);
      setIsScanning(false); // Stop scanning during connection
      // Automatically attempt connection
      connectWithIp(extractedIp);
    } else {
      setIsScanning(false); // Stop scanning to prevent repeated errors
      Alert.alert(
        "QR Code Error",
        "No valid IP address found in QR code. Please try a different QR code.",
        [
          {
            text: "OK",
            onPress: () => setIsScanning(true) // Resume scanning after user acknowledges
          }
        ]
      );
    }
  };

  const connectWithIp = async (ipToConnect) => {
    const ipToUse = ipToConnect || ip;
    if (!ipToUse.trim()) {
      Alert.alert("Error", "Please enter a sync code");
      return;
    }

    try {
      let finalIp = ipToUse.trim();

      // Handle different IP input formats
      if (finalIp.includes(".")) {
        // If it contains dots, assume it's a complete IP
        if (!finalIp.startsWith("192.168.")) {
          // If it doesn't start with 192.168., add it
          finalIp = `192.168.${finalIp}`;
        }
      } else {
        // If no dots, assume it's just the last two octets
        finalIp = `192.168.${finalIp}`;
      }

      // Test connection to the server
      const testUrl = `http://${finalIp}:8500/pos/ping`;
      const res = await axios.get(testUrl, {
        timeout: 3000,
      });

      if (res.status === 200) {
        // Store the IP parts and full URL
        await AsyncStorage.setItem("ip", ipToUse.trim());
        await AsyncStorage.setItem("fullip", `http://${finalIp}:8500`);

        console.log("Connected to:", `http://${finalIp}:8500`);
        router.replace("/home");
      }
    } catch (err) {
      console.log("Error details:", err);
      const errorMessage = err.message || "Invalid IP or server not reachable";
      Alert.alert(
        "Connection Failed",
        errorMessage,
        [
          {
            text: "OK",
            onPress: () => setIsScanning(true) // Resume scanning after connection error
          }
        ]
      );
    }
  };

  const connect = async () => {
    await connectWithIp();
  };
  if (hasPermission === null) {
    return (
      <View style={styles.container}>
        <Text>Requesting camera permission...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>No access to camera</Text>
        <TouchableOpacity style={styles.manualButton} onPress={() => setShowManualInput(true)}>
          <Text style={styles.manualButtonText}>Link Manually</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.fullScreenContainer}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Full Screen Camera Background */}
      <CameraView
        style={styles.fullScreenCamera}
        onBarcodeScanned={isScanning ? handleQrCodeScanned : undefined}
        barcodeScannerSettings={{
          barcodeTypes: ['qr'],
        }}
      />

      {/* Overlay with cutout for scanning area */}
      <View style={styles.overlayContainer}>
        {/* Top overlay section */}
        <View style={styles.overlayTop}>
          {/* Top Section - Logo and Title */}
          <View style={styles.topSection}>
            <Image
              source={require('../assets/images/1logo.png')}
              style={styles.logoImage}
              resizeMode="contain"
            />
            <Text style={styles.logoText}>Foodyqueen</Text>
            <Text style={styles.subtitle}>Scan QR from foodyqueen POS</Text>
          </View>
        </View>

        {/* Middle section with clear scanning area */}
        <View style={styles.middleRow}>
          <View style={styles.overlaySide} />
          <View style={styles.clearScanningArea}>
            {/* Scanning reticle/frame - corners only */}
            <View style={styles.scannerCorners}>
              <View style={[styles.corner, styles.topLeft]} />
              <View style={[styles.corner, styles.topRight]} />
              <View style={[styles.corner, styles.bottomLeft]} />
              <View style={[styles.corner, styles.bottomRight]} />
            </View>
          </View>
          <View style={styles.overlaySide} />
        </View>

        {/* Bottom overlay section */}
        <View style={styles.overlayBottom}>
          {/* Manual Link Button */}
          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => setShowManualInput(true)}
          >
            <Image
              source={require('../assets/images/3link.png')}
              style={styles.linkIcon}
              resizeMode="contain"
            />
            <Text style={styles.linkButtonText}>Link Manually</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Bottom Drawer - Always visible as part of UI design */}
      <View style={styles.bottomDrawer}>
        <View style={styles.drawerContent}>
          <Text style={styles.drawerTitle}>Scan QR from foodyqueen POS</Text>
          <Text style={styles.drawerMessage}>Position the QR code within the frame above</Text>
          <Image
            source={require('../assets/images/2illu.png')}
            style={styles.illustrationImage}
            resizeMode="contain"
          />
        </View>
      </View>

      {/* Manual Input Modal */}
      <Modal
        visible={showManualInput}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowManualInput(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Manual Connection</Text>
            <Text style={styles.modalSubtitle}>
              Enter IP address or last two octets (e.g., 1.100 or *************)
            </Text>
            <TextInput
              value={ip}
              keyboardType="numeric"
              onChangeText={setIp}
              placeholder="e.g., 1.100 or *************"
              placeholderTextColor="#888"
              style={styles.input}
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowManualInput(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.connectButton}
                onPress={() => {
                  setShowManualInput(false);
                  connect();
                }}
              >
                <Text style={styles.connectButtonText}>Connect</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  // Full screen container
  fullScreenContainer: {
    flex: 1,
    backgroundColor: '#000000',
  },

  // Full screen camera
  fullScreenCamera: {
    flex: 1,
    width: '100%',
    height: '100%',
  },

  // Overlay container with cutout effect
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },

  // Top overlay section (darkened)
  overlayTop: {
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    paddingTop: 80,
    paddingHorizontal: 20,
  },

  // Middle row with sides darkened and center clear
  middleRow: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },

  // Side overlays (darkened)
  overlaySide: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    height: '100%',
  },

  // Clear scanning area (no overlay)
  clearScanningArea: {
    width: width - 80,
    height: width - 80,
    backgroundColor: 'transparent',
    position: 'relative',
  },

  // Bottom overlay section (darkened)
  overlayBottom: {
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 40,
  },

  // Top section with logo
  topSection: {
    alignItems: 'center',
    paddingBottom: 20,
  },

  logoImage: {
    width: 140,
    height: 50,
    marginBottom: 8,
  },

  logoText: {
    fontSize: 0,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },

  subtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.8,
  },

  // Center section with QR focus area
  centerSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },

  scannerFocusArea: {
    width: width - 80,
    height: width - 80,
    backgroundColor: 'transparent', // Transparent to allow camera view
    borderRadius: 20,
    marginBottom: 30, // Space between focus area and link button
    position: 'relative',
  },

  // Scanning corners/reticle
  scannerCorners: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },

  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#FFFFFF',
    borderWidth: 3,
  },

  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    borderTopLeftRadius: 20,
  },

  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
    borderTopRightRadius: 20,
  },

  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
    borderBottomLeftRadius: 20,
  },

  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderBottomRightRadius: 20,
  },

  // Spacer to push content to bottom
  spacer: {
    flex: 1,
  },

  linkButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },

  linkIcon: {
    width: 20,
    height: 20,
    marginRight: 8,
    tintColor: '#FFFFFF',
  },

  linkButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },

  // Bottom drawer for error state
  bottomDrawer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingTop: 20,
    paddingHorizontal: 30,
    paddingBottom: 30,
    minHeight: 150,
    zIndex: 1000, // Ensure it appears above other elements
    elevation: 10, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },

  drawerContent: {
    alignItems: 'center',
  },

  drawerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
    textAlign: 'center',
  },

  drawerMessage: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },

  illustrationImage: {
    width: 80,
    height: 80,
    marginTop: 15,
  },

  // Legacy styles for permission screens
  container: {
    flex: 1,
    backgroundColor: '#2C2C2C',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },

  errorText: {
    color: '#FF6B6B',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },

  manualButton: {
    backgroundColor: '#4A4A4A',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 25,
    alignItems: 'center',
  },

  manualButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 30,
    marginHorizontal: 40,
    width: width - 80,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 10,
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },
  input: {
    borderWidth: 1,
    borderColor: '#DDDDDD',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F0F0F0',
    paddingVertical: 12,
    borderRadius: 10,
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '500',
  },
  connectButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    borderRadius: 10,
    marginLeft: 10,
    alignItems: 'center',
  },
  connectButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
