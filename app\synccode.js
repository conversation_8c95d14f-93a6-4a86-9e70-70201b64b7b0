import AsyncStorage from "@react-native-async-storage/async-storage";
import axios from "axios";
import { useRouter } from "expo-router";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Text, TextInput, View } from "react-native";

export default function SyncCodeScreen() {
  const [ip, setIp] = useState("");
  const router = useRouter();

  useEffect(() => {
    const getIt = async () => {
      let result = await AsyncStorage.getItem("ip");
      setIp(result || ""); // Handle null case
    };
    getIt();
  }, []);
  const connect = async () => {
    if (!ip.trim()) {
      Alert.alert("Error", "Please enter a sync code");
      return;
    }

    try {
      let finalIp = ip.trim();

      // Handle different IP input formats
      if (finalIp.includes(".")) {
        // If it contains dots, assume it's a complete IP
        if (!finalIp.startsWith("192.168.")) {
          // If it doesn't start with 192.168., add it
          finalIp = `192.168.${finalIp}`;
        }
      } else {
        // If no dots, assume it's just the last two octets
        finalIp = `192.168.${finalIp}`;
      }

      // Test connection to the server
      const testUrl = `http://${finalIp}:8500/pos/ping`;
      const res = await axios.get(testUrl, {
        timeout: 3000,
      });

      if (res.status === 200) {
        // Store the IP parts and full URL
        await AsyncStorage.setItem("ip", ip.trim());
        await AsyncStorage.setItem("fullip", `http://${finalIp}:8500`);

        console.log("Connected to:", `http://${finalIp}:8500`);
        router.replace("/home");
      }
    } catch (err) {
      console.log("Error details:", err);
      Alert.alert(
        "Connection Failed",
        err.message || "Invalid IP or server not reachable"
      );
    }
  };
  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        padding: 20,
        background: "white",
      }}
    >
      {" "}
      <Text style={{ fontWeight: "bold", fontSize: 24 }}>Sync Code</Text>{" "}
      <Text>
        Enter IP address or last two octets (e.g., 1.100 or *************)
      </Text>{" "}
      <TextInput
        value={ip}
        keyboardType="numeric"
        onChangeText={setIp}
        placeholder="e.g., 1.100 or *************"
        placeholderTextColor="#888"
        style={{
          borderWidth: 1,
          marginBottom: 20,
          borderRadius: 10,
          marginVertical: 10,
          padding: 10,
        }}
      />
      <Button title="Connect" onPress={connect} />
    </View>
  );
}
